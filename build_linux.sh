#!/bin/bash

# Linux用ビルドスクリプト
# 15Puzzle-C プロジェクト

echo "=== 15Puzzle-C Linux Build Script ==="

# 必要な依存関係をチェック
echo "Checking dependencies..."

# PulseAudio開発ライブラリのチェック
if ! pkg-config --exists libpulse; then
    echo "Error: PulseAudio development libraries not found."
    echo "Please install them using:"
    echo "  Ubuntu/Debian: sudo apt-get install libpulse-dev"
    echo "  Fedora/RHEL:   sudo dnf install pulseaudio-libs-devel"
    echo "  Arch Linux:    sudo pacman -S libpulse"
    exit 1
fi

# GLFW開発ライブラリのチェック
if ! pkg-config --exists glfw3; then
    echo "Error: GLFW3 development libraries not found."
    echo "Please install them using:"
    echo "  Ubuntu/Debian: sudo apt-get install libglfw3-dev"
    echo "  Fedora/RHEL:   sudo dnf install glfw-devel"
    echo "  Arch Linux:    sudo pacman -S glfw-wayland"
    exit 1
fi

# OpenGL開発ライブラリのチェック
if ! pkg-config --exists gl; then
    echo "Error: OpenGL development libraries not found."
    echo "Please install them using:"
    echo "  Ubuntu/Debian: sudo apt-get install libgl1-mesa-dev libglu1-mesa-dev"
    echo "  Fedora/RHEL:   sudo dnf install mesa-libGL-devel mesa-libGLU-devel"
    echo "  Arch Linux:    sudo pacman -S mesa glu"
    exit 1
fi

# GLEW開発ライブラリのチェック
if ! pkg-config --exists glew; then
    echo "Error: GLEW development libraries not found."
    echo "Please install them using:"
    echo "  Ubuntu/Debian: sudo apt-get install libglew-dev"
    echo "  Fedora/RHEL:   sudo dnf install glew-devel"
    echo "  Arch Linux:    sudo pacman -S glew"
    exit 1
fi

# FreeType開発ライブラリのチェック
if ! pkg-config --exists freetype2; then
    echo "Error: FreeType development libraries not found."
    echo "Please install them using:"
    echo "  Ubuntu/Debian: sudo apt-get install libfreetype6-dev"
    echo "  Fedora/RHEL:   sudo dnf install freetype-devel"
    echo "  Arch Linux:    sudo pacman -S freetype2"
    exit 1
fi

echo "All dependencies found!"

# xmakeがインストールされているかチェック
if ! command -v xmake &> /dev/null; then
    echo "Error: xmake is not installed."
    echo "Please install xmake from: https://xmake.io/"
    echo "Or use the installation script:"
    echo "  curl -fsSL https://xmake.io/shget.text | bash"
    exit 1
fi

echo "Building project with xmake..."

# プロジェクトの設定
xmake f -p linux -a x86_64 -m release

# ビルド実行
xmake build

if [ $? -eq 0 ]; then
    echo "=== Build completed successfully! ==="
    echo "Executable location: build/linux/x86_64/release/15Puzzle-C"
    echo ""
    echo "To run the application:"
    echo "  ./build/linux/x86_64/release/15Puzzle-C"
    echo ""
    echo "Or use xmake to run:"
    echo "  xmake run"
else
    echo "=== Build failed! ==="
    echo "Please check the error messages above."
    exit 1
fi
