#!/bin/bash

# Linux用依存関係インストールスクリプト
# 15Puzzle-C プロジェクト

echo "=== Installing dependencies for 15Puzzle-C on Linux ==="

# ディストリビューションを検出
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
elif type lsb_release >/dev/null 2>&1; then
    OS=$(lsb_release -si)
    VER=$(lsb_release -sr)
elif [ -f /etc/lsb-release ]; then
    . /etc/lsb-release
    OS=$DISTRIB_ID
    VER=$DISTRIB_RELEASE
elif [ -f /etc/debian_version ]; then
    OS=Debian
    VER=$(cat /etc/debian_version)
elif [ -f /etc/SuSe-release ]; then
    OS=openSUSE
elif [ -f /etc/redhat-release ]; then
    OS=RedHat
else
    OS=$(uname -s)
    VER=$(uname -r)
fi

echo "Detected OS: $OS"

# パッケージマネージャーに基づいて依存関係をインストール
case "$OS" in
    *Ubuntu*|*Debian*)
        echo "Installing dependencies for Ubuntu/Debian..."
        sudo apt-get update
        sudo apt-get install -y \
            build-essential \
            cmake \
            pkg-config \
            libpulse-dev \
            libglfw3-dev \
            libgl1-mesa-dev \
            libglu1-mesa-dev \
            libglew-dev \
            libfreetype6-dev \
            libfontconfig1-dev \
            libiconv-hook-dev \
            libglm-dev
        ;;
    *Fedora*|*CentOS*|*RHEL*|*RedHat*)
        echo "Installing dependencies for Fedora/RHEL/CentOS..."
        sudo dnf install -y \
            gcc-c++ \
            cmake \
            pkg-config \
            pulseaudio-libs-devel \
            glfw-devel \
            mesa-libGL-devel \
            mesa-libGLU-devel \
            glew-devel \
            freetype-devel \
            fontconfig-devel \
            glm-devel
        ;;
    *Arch*)
        echo "Installing dependencies for Arch Linux..."
        sudo pacman -S --needed \
            base-devel \
            cmake \
            pkg-config \
            libpulse \
            glfw-wayland \
            mesa \
            glu \
            glew \
            freetype2 \
            fontconfig \
            glm
        ;;
    *openSUSE*)
        echo "Installing dependencies for openSUSE..."
        sudo zypper install -y \
            gcc-c++ \
            cmake \
            pkg-config \
            libpulse-devel \
            glfw3-devel \
            Mesa-libGL-devel \
            Mesa-libGLU-devel \
            glew-devel \
            freetype2-devel \
            fontconfig-devel \
            glm-devel
        ;;
    *)
        echo "Unsupported distribution: $OS"
        echo "Please install the following packages manually:"
        echo "- build-essential/gcc-c++"
        echo "- cmake"
        echo "- pkg-config"
        echo "- PulseAudio development libraries"
        echo "- GLFW3 development libraries"
        echo "- OpenGL development libraries (Mesa)"
        echo "- GLEW development libraries"
        echo "- FreeType development libraries"
        echo "- FontConfig development libraries"
        echo "- GLM (OpenGL Mathematics) libraries"
        exit 1
        ;;
esac

echo "=== Dependencies installation completed! ==="
echo ""
echo "Next steps:"
echo "1. Install xmake if not already installed:"
echo "   curl -fsSL https://xmake.io/shget.text | bash"
echo "2. Run the build script:"
echo "   ./build_linux.sh"
