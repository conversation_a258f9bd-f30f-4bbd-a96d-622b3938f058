#ifndef PUZZLE_RENDERER_H
#define PUZZLE_RENDERER_H

#include <GL/glew.h>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>
#include <vector>
#include "shader.h"
#include "text_renderer.h"
#include "puzzle.h"

class PuzzleRenderer {
public:
    // コンストラクタ
    PuzzleRenderer(unsigned int width, unsigned int height);
    ~PuzzleRenderer();

    // パズルの描画
    void render(Puzzle& puzzle, double mouseX, double mouseY);
    
    // マウスクリック処理
    bool handleMouseClick(Puzzle& puzzle, double mouseX, double mouseY);

private:
    // ウィンドウサイズ
    unsigned int width, height;
    
    // タイルのサイズと間隔
    float tileSize;
    float tileSpacing;
    
    // 描画開始位置
    float startX, startY;
    
    // タイルの色
    glm::vec3 normalColor;
    glm::vec3 hoverColor;
    glm::vec3 solvedColor;
    
    // シェーダー
    Shader* tileShader;
    
    // テキストレンダラー
    TextRenderer* textRenderer;
    
    // VAOとVBO
    unsigned int VAO, VBO, EBO;
    
    // 頂点データとインデックスデータ
    std::vector<float> vertices;
    std::vector<unsigned int> indices;
    
    // 初期化
    void initialize();
    
    // タイルの座標計算
    glm::vec2 getTilePosition(int row, int col);
    
    // マウス座標からタイルの位置を計算
    bool getTileAtPosition(double mouseX, double mouseY, int& row, int& col);
};

#endif
