#ifndef SIMPLE_PUZZLE_RENDERER_H
#define SIMPLE_PUZZLE_RENDERER_H

#include <GL/glew.h>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>
#include <vector>
#include <string>
#include "puzzle.h"

class SimplePuzzleRenderer {
public:
    // コンストラクタ
    SimplePuzzleRenderer(unsigned int width, unsigned int height);
    ~SimplePuzzleRenderer();

    // パズルの描画
    void render(Puzzle& puzzle, double mouseX, double mouseY, float currentTime);

    // マウスクリック処理
    bool handleMouseClick(Puzzle& puzzle, double mouseX, double mouseY);

    // マウス座標からタイルの位置を計算
    bool getTileAtPosition(double mouseX, double mouseY, int& row, int& col) const;

    // Call this when a tile starts moving
    // tileValue: The number on the tile being moved
    // fromGridR, fromGridC: Grid coordinates the tile is moving FROM
    // toGridR, toGridC: Grid coordinates the tile is moving TO
    void startTileMoveAnimation(int tileValue, int fromGridR, int fromGridC, int toGridR, int toGridC);

    // Call this when the puzzle is reset to animate all tiles to their correct positions
    void startResetAnimation(const Puzzle& puzzle);

    // Check if any tile is currently animating
    bool isAnimationRunning() const;

private:
    // ウィンドウサイズ
    unsigned int width, height;

    // タイルのサイズと間隔
    float tileSize;
    float tileSpacing;
    std::vector<GLuint> tileTextures; // Added to store tile textures

    // 描画開始位置
    float startX, startY;

    // タイルの色
    glm::vec3 normalColor;
    glm::vec3 hoverColor;
    glm::vec3 solvedColor;

    // アニメーション中のタイルの情報
    struct AnimatingTile {
        int value;                      // タイルの値
        glm::vec2 startScreenPos;       // 開始位置（画面座標）
        glm::vec2 currentScreenPos;     // 現在位置（画面座標）
        glm::vec2 targetScreenPos;      // 目標位置（画面座標）
    };

    // For tile movement animation
    int movingTileValue;                // Value of the tile currently moving (0 if none) - 単一タイルアニメーション用（後方互換性）
    glm::vec2 movingTileStartScreenPos;   // Screen coordinates (top-left) where the tile started
    glm::vec2 movingTileCurrentScreenPos; // Current screen coordinates (top-left) during animation
    glm::vec2 movingTileTargetScreenPos;  // Screen coordinates (top-left) where the tile is going

    std::vector<AnimatingTile> animatingTiles; // 複数タイルのアニメーション用
    bool isResetAnimation;              // リセットアニメーション中かどうか
    float animationStartTime;           // Time when the animation started (glfwGetTime())
    static constexpr float TILE_MOVE_DURATION = 0.15f; // Duration of the move animation in seconds
    static constexpr float RESET_ANIMATION_DURATION = 0.5f; // リセットアニメーションの時間（秒）

    // OpenGL 1.x/2.x スタイルでは VAO/VBO は不要

    // 初期化
    void initialize();

    // タイルの描画（旧バージョン）
    void drawTile(float x, float y, int value, bool isHovered, bool isCorrectlyPlaced);

    // タイルの描画（新バージョン）
    void drawTile(float x, float y, float size, const glm::vec3& bgColor, bool isRounded) const;

    // 数字の描画（旧バージョン）
    void drawNumber(float x, float y, int number, bool isCorrectlyPlaced);

    // 数字の描画（新バージョン）
    void drawNumber(int number, float x, float y, float currentTileSize, const glm::vec3& textColor, bool isCorrectlyPlaced) const;

    // Helper to get screen coordinates (top-left) for a grid cell
    glm::vec2 getScreenPosForGrid(int r, int c) const;

    // Easing functions for smooth animation
    float easeInOutQuad(float t) const;
    float easeInOutCubic(float t) const;
    float easeInOutQuart(float t) const;
    float easeInOutElastic(float t) const;
    float easeOutBounce(float t) const;
};

#endif
