#pragma once

#include <string>
#include <memory>

#ifdef _WIN32
#include <windows.h>
#include <mmsystem.h>
#endif

/**
 * シンプルな音声再生システム
 * Windows環境でWAVファイルの再生をサポート
 */
class AudioSystem {
public:
    AudioSystem();
    ~AudioSystem();

    /**
     * 音声システムを初期化
     * @return 初期化が成功した場合true
     */
    bool initialize();

    /**
     * 音声システムを終了
     */
    void shutdown();

    /**
     * メモリ上のWAVデータから音声を再生
     * @param wavData WAVファイルのバイナリデータ
     * @param dataSize データのサイズ
     * @param loop ループ再生するかどうか
     * @return 再生が開始された場合true
     */
    bool playWavFromMemory(const unsigned char* wavData, size_t dataSize, bool loop = false);

    /**
     * ファイルからWAVを再生
     * @param filename WAVファイルのパス
     * @param loop ループ再生するかどうか
     * @return 再生が開始された場合true
     */
    bool playWavFromFile(const std::string& filename, bool loop = false);

    /**
     * 現在再生中の音声を停止
     */
    void stopAll();

    /**
     * 音声システムが初期化されているかチェック
     * @return 初期化されている場合true
     */
    bool isInitialized() const { return initialized; }

private:
    bool initialized;

#ifdef _WIN32
    // Windows固有の実装用メンバー
    struct WavData {
        std::unique_ptr<unsigned char[]> data;
        size_t size;
    };
    
    WavData currentWavData;
#endif
};

// グローバルな音声システムインスタンス
extern AudioSystem g_audioSystem;
