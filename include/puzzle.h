#ifndef PUZZLE_H
#define PUZZLE_H

#include <cstdlib>
#include <ctime>
#include <iostream>

// パズルのサイズ定義
constexpr int PUZZLE_SIZE = 4;
constexpr int TILE_COUNT = (PUZZLE_SIZE * PUZZLE_SIZE - 1);

// パズルのクラス
class Puzzle {
private:
    int board[PUZZLE_SIZE][PUZZLE_SIZE];  // パズルの盤面（0は空きスペース）
    int emptyRow;                         // 空きスペースの行位置
    int emptyCol;                         // 空きスペースの列位置
    int moves;                            // 移動回数
    bool solved;                          // 解決済みかどうか
    unsigned int currentSeed;             // 現在のシャッフルに使用されたシード値

public:
    // コンストラクタ
    Puzzle();

    // パズルをシャッフル
    void shuffle(int moves, unsigned int seed = 0); // seed パラメータを追加 (デフォルト値付き)

    // タイルを移動（行と列の位置を指定）
    bool moveTile(int row, int col);

    // パズルが解決されたかチェック
    bool checkSolved();

    // パズルをリセット（初期状態に戻す）
    void reset();

    // ゲッター
    int getTile(int row, int col) const { return board[row][col]; }
    int getMoves() const { return moves; }
    bool isSolved() const { return solved; }
    int getEmptyRow() const { return emptyRow; } // 追加
    int getEmptyCol() const { return emptyCol; } // 追加
    unsigned int getCurrentSeed() const { return currentSeed; } // シード値取得用ゲッター

    // 特定のタイルが正しい位置にあるかチェック
    bool isTileCorrect(int row, int col) const;
};

#endif // PUZZLE_H
