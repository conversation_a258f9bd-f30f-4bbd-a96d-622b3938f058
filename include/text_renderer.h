#ifndef TEXT_RENDERER_H
#define TEXT_RENDERER_H

#include <GL/glew.h>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <ft2build.h>
#include FT_FREETYPE_H
#include <map>
#include <string>
#include "shader.h"

struct Character {
    unsigned int TextureID;  // グリフのテクスチャID
    glm::ivec2   Size;       // グリフのサイズ
    glm::ivec2   Bearing;    // ベースラインからグリフの左上までの距離
    unsigned int Advance;    // 次のグリフまでの水平距離
};

class TextRenderer {
public:
    std::map<char, Character> Characters;
    Shader* shader;

    TextRenderer(unsigned int width, unsigned int height);
    ~TextRenderer();
    void Load(std::string font, unsigned int fontSize);
    void RenderText(std::string text, float x, float y, float scale, glm::vec3 color);

private:
    unsigned int VAO, VBO;
};

#endif
