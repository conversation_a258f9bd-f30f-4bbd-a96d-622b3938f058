#ifndef SHADER_H
#define SHADER_H

#include <GL/glew.h>
#include <string>
#include <fstream>
#include <sstream>
#include <iostream>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>

class Shader {
public:
    // プログラムID
    unsigned int ID;

    // コンストラクタ: シェーダーを読み込み、コンパイル
    Shader(const char* vertexPath, const char* fragmentPath) {
        // 1. ファイルからシェーダーコードを取得
        std::string vertexCode;
        std::string fragmentCode;
        std::ifstream vShaderFile;
        std::ifstream fShaderFile;

        // 例外処理
        vShaderFile.exceptions(std::ifstream::failbit | std::ifstream::badbit);
        fShaderFile.exceptions(std::ifstream::failbit | std::ifstream::badbit);
        try {
            // ファイルを開く
            vShaderFile.open(vertexPath);
            fShaderFile.open(fragmentPath);
            std::stringstream vShaderStream, fShaderStream;

            // ファイルの内容をストリームに読み込む
            vShaderStream << vShaderFile.rdbuf();
            fShaderStream << fShaderFile.rdbuf();

            // ファイルを閉じる
            vShaderFile.close();
            fShaderFile.close();

            // ストリームを文字列に変換
            vertexCode = vShaderStream.str();
            fragmentCode = fShaderStream.str();
        }
        catch (std::ifstream::failure& e) {
            std::string errorMsg = "ERROR::SHADER::FILE_NOT_SUCCESSFULLY_READ: ";
            errorMsg += e.what();
            errorMsg += "\nVertex path: ";
            errorMsg += vertexPath;
            errorMsg += "\nFragment path: ";
            errorMsg += fragmentPath;
            std::cerr << errorMsg << std::endl;
            throw std::runtime_error(errorMsg);
        }

        if (vertexCode.empty() || fragmentCode.empty()) {
            std::string errorMsg = "ERROR::SHADER::EMPTY_SHADER_CODE";
            std::cerr << errorMsg << std::endl;
            throw std::runtime_error(errorMsg);
        }

        const char* vShaderCode = vertexCode.c_str();
        const char* fShaderCode = fragmentCode.c_str();

        // 2. シェーダーをコンパイル
        unsigned int vertex, fragment;
        int success;
        char infoLog[512];

        // 頂点シェーダー
        vertex = glCreateShader(GL_VERTEX_SHADER);
        glShaderSource(vertex, 1, &vShaderCode, NULL);
        glCompileShader(vertex);
        // コンパイルエラーをチェック
        glGetShaderiv(vertex, GL_COMPILE_STATUS, &success);
        if (!success) {
            glGetShaderInfoLog(vertex, 512, NULL, infoLog);
            std::string errorMsg = "ERROR::SHADER::VERTEX::COMPILATION_FAILED\n";
            errorMsg += infoLog;
            std::cerr << errorMsg << std::endl;
            throw std::runtime_error(errorMsg);
        }

        // フラグメントシェーダー
        fragment = glCreateShader(GL_FRAGMENT_SHADER);
        glShaderSource(fragment, 1, &fShaderCode, NULL);
        glCompileShader(fragment);
        // コンパイルエラーをチェック
        glGetShaderiv(fragment, GL_COMPILE_STATUS, &success);
        if (!success) {
            glGetShaderInfoLog(fragment, 512, NULL, infoLog);
            std::string errorMsg = "ERROR::SHADER::FRAGMENT::COMPILATION_FAILED\n";
            errorMsg += infoLog;
            std::cerr << errorMsg << std::endl;
            glDeleteShader(vertex); // 頂点シェーダーを解放
            throw std::runtime_error(errorMsg);
        }

        // シェーダープログラム
        ID = glCreateProgram();
        glAttachShader(ID, vertex);
        glAttachShader(ID, fragment);
        glLinkProgram(ID);
        // リンクエラーをチェック
        glGetProgramiv(ID, GL_LINK_STATUS, &success);
        if (!success) {
            glGetProgramInfoLog(ID, 512, NULL, infoLog);
            std::string errorMsg = "ERROR::SHADER::PROGRAM::LINKING_FAILED\n";
            errorMsg += infoLog;
            std::cerr << errorMsg << std::endl;
            glDeleteShader(vertex);
            glDeleteShader(fragment);
            throw std::runtime_error(errorMsg);
        }

        // シェーダーは、リンク後に不要になるので削除
        glDeleteShader(vertex);
        glDeleteShader(fragment);
    }

    // シェーダープログラムを有効化
    void use() {
        glUseProgram(ID);
    }

    // ユニフォーム変数の設定
    void setBool(const std::string &name, bool value) const {
        glUniform1i(glGetUniformLocation(ID, name.c_str()), (int)value);
    }
    void setInt(const std::string &name, int value) const {
        glUniform1i(glGetUniformLocation(ID, name.c_str()), value);
    }
    void setFloat(const std::string &name, float value) const {
        glUniform1f(glGetUniformLocation(ID, name.c_str()), value);
    }
    void setVec3(const std::string &name, const glm::vec3 &value) const {
        glUniform3fv(glGetUniformLocation(ID, name.c_str()), 1, &value[0]);
    }
    void setVec3(const std::string &name, float x, float y, float z) const {
        glUniform3f(glGetUniformLocation(ID, name.c_str()), x, y, z);
    }
    void setMat4(const std::string &name, const glm::mat4 &mat) const {
        glUniformMatrix4fv(glGetUniformLocation(ID, name.c_str()), 1, GL_FALSE, &mat[0][0]);
    }
};

#endif
