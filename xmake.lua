add_rules("mode.debug", "mode.release")

-- プロジェクト設定
set_project("15Puzzle-C")
set_version("1.0.0")
set_languages("c11", "cxx17")

-- コンパイラの設定（Mingw）
if is_plat("windows") then
    -- MinG<PERSON>の明示的なパスを指定
    set_toolchains("mingw")
end

-- システムにインストールされたskiaを使用
add_requires("libiconv")
add_requires("freetype")
add_requires("fontconfig")
-- add_requires("imgui")
add_requires("glfw")
add_requires("opengl")
add_requires("glew")
add_requires("glm")

-- Linux用のオーディオライブラリはシステムライブラリとして直接リンク

if is_mode("debug") then
    set_config("debugger", "gdb") -- 新しい構文
    print("Debug mode: debugger should be called, using gdb.")
end

-- アプリケーションのターゲット設定
target("15Puzzle-C")
    set_kind("binary")
    add_rules("utils.bin2c", {extensions = {".png", ".vs", ".fs", ".ttf", ".wav"}, prefixname = "g_"})

    -- Warning and optimization settings
    add_cflags("-O3 -mtune=native -march=native -mfpmath=both")
    add_cxxflags("-O3 -mtune=native -march=native -mfpmath=both")
    add_ldflags("-O3 -mtune=native -march=native -mfpmath=both")

    -- Settings for GDB debugging
    if is_mode("debug") then
        set_symbols("debug")
        set_optimize("none")
        add_cflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_cxxflags("-g3", "-fstack-protector-all", "-fno-omit-frame-pointer")
        add_ldflags("-g3")
        add_defines("DEBUG", "DEBUG_GL", "GL_DEBUG")
        print("Debug mode: target debug mode called.")

        -- デバッグ情報を強化
        if is_plat("windows", "mingw") then
            add_cxflags("-gdwarf-4")  -- DWARF形式のデバッグ情報
            add_ldflags("-Wl,--export-all-symbols") -- すべてのシンボルをエクスポート
        end
    end

    -- ソースファイルの追加
    add_files("src/imgui/*.cpp")
    -- ルートディレクトリのファイル
    add_files("src/*.cpp")
    -- add_files("resources/textures/*.png")
    -- add_files("resources/fonts/*.ttf")
    -- シェーダーファイルが存在しない場合はコメントアウト
    -- add_files("resources/shaders/*.vs")
    -- add_files("resources/shaders/*.fs")
    -- 音声ファイルの追加
    add_files("resources/sounds/*.wav")

    -- インクルードパスとライブラリ設定
    add_packages("libiconv", "freetype", "fontconfig","glfw", "opengl", "glew", "glm")

    add_includedirs("include") -- include ディレクトリ全体をインクルードパスに追加
    add_includedirs("include/imgui") -- imgui のヘッダーファイル用

    -- プラットフォーム別のbin2c生成パス
    if is_plat("windows", "mingw") then
        add_includedirs("build/.gens/15Puzzle-C/mingw/x86_64/release/rules/utils/bin2c")
    elseif is_plat("linux") then
        add_includedirs("build/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c")
    end

    -- imgui
    add_packages("imgui")

    -- OpenGLのライブラリを追加
    if is_plat("windows", "mingw") then
        add_links("opengl32", "glu32", "jpeg", "png", "webp", "webpdemux", "webpmux")
    elseif is_plat("linux") then
        add_links("GL", "GLU", "pulse", "pulse-simple")
        -- 画像・動画ライブラリは必要に応じて追加
        -- add_links("jpeg", "png", "webp", "webpdemux", "webpmux", "avformat", "avcodec", "avutil", "swscale", "swresample")
    elseif is_plat("macosx") then
        add_frameworks("OpenGL", "AudioToolbox", "CoreAudio")
        -- add_links("jpeg", "png", "webp", "webpdemux", "webpmux", "avformat", "avcodec", "avutil", "swscale", "swresample")
    end
    add_defines(
        "PLATFORM_DESKTOP",
        "IMGUI_IMPL_OPENGL_LOADER_GLEW"
    )

    -- プラットフォーム別設定
    if is_plat("windows", "mingw") then
        add_syslinks("ole32", "user32", "gdi32", "comdlg32", "pthread", "uuid", "winmm")
        add_cflags("-fopenmp")
        add_ldflags("-fopenmp")
    elseif is_plat("linux") then
        add_syslinks("pthread", "pulse", "pulse-simple", "m", "dl")
        add_cflags("-fopenmp")
        add_ldflags("-fopenmp")
    else
        add_syslinks("pthread")
        add_cflags("-fopenmp")
        add_ldflags("-fopenmp")
    end
