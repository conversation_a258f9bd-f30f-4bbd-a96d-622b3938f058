#include <iostream>
#include <string>
#include <cmath>
#include <fstream>
#include <direct.h> // for _getcwd
#include <chrono> // 追加
#include <ctime>   // 追加
#include <vector> // 追加
#include <algorithm> // 追加
#include <iomanip> // 追加
#include <sstream> // 追加

// GLFW + OpenGL
#include <GL/glew.h>
#include <GLFW/glfw3.h>
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>

// ImGui (UIのみに使用)
#include "imgui/imgui.h"
#include "imgui/imgui_impl_glfw.h"
#include "imgui/imgui_impl_opengl3.h"

// 15パズルのロジック
#include "../include/puzzle.h"
#include "../include/simple_puzzle_renderer.h"
#include "../include/audio_system.h"
#include "../include/embedded_resources.h"

// ウィンドウサイズ
constexpr int WINDOW_WIDTH = 800;
constexpr int WINDOW_HEIGHT = 600;

// マウス状態
double mouseX = 0.0, mouseY = 0.0;
bool mousePressed = false;
std::vector<std::string> solvedTimesLog; // 解けた時間のログ
bool wasPuzzleSolvedInPreviousFrame = false; // 直前のフレームでパズルが解けていたか
bool isTimerRunning = true; // タイマーが動作中かどうかのフラグ
std::chrono::steady_clock::duration finalSolvedDuration; // 最後に解いた時の経過時間

// GLFWエラーコールバック
static void glfw_error_callback(int error, const char* description) {
    std::cerr << "GLFW Error " << error << ": " << description << std::endl;
}

// マウスボタンコールバック
static void mouse_button_callback(GLFWwindow* window, int button, int action, int mods) {
    if (button == GLFW_MOUSE_BUTTON_LEFT) {
        if (action == GLFW_PRESS) {
            mousePressed = true;
        } else if (action == GLFW_RELEASE) {
            mousePressed = false;
        }
    }
}

// マウス位置コールバック
static void cursor_position_callback(GLFWwindow* window, double xpos, double ypos) {
    mouseX = xpos;
    mouseY = ypos;
}

int main(int argc, char* argv[]) {
    // GLFWの初期化
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) {
        std::cerr << "Failed to initialize GLFW" << std::endl;
        return 1;
    }

    // OpenGL 2.1 (互換性モード)
    const char* glsl_version = "#version 120";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 2);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 1);
    glfwWindowHint(GLFW_RESIZABLE, GLFW_FALSE); // ウィンドウリサイズを無効化

    // ウィンドウの作成
    GLFWwindow* window = glfwCreateWindow(WINDOW_WIDTH, WINDOW_HEIGHT, "15 Puzzle", nullptr, nullptr);
    if (window == nullptr) {
        std::cerr << "Failed to create GLFW window" << std::endl;
        glfwTerminate();
        return 1;
    }
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // VSync有効

    // マウスコールバックの設定
    glfwSetMouseButtonCallback(window, mouse_button_callback);
    glfwSetCursorPosCallback(window, cursor_position_callback);

    // GLEWの初期化
    glewExperimental = GL_TRUE;
    GLenum err = glewInit();
    if (err != GLEW_OK) {
        std::cerr << "Failed to initialize GLEW: " << glewGetErrorString(err) << std::endl;
        glfwTerminate();
        return 1;
    }

    // OpenGLの設定
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    // シェーダーファイルの存在確認
    std::ifstream vertFile("./src/shaders/tile.vert");
    std::ifstream fragFile("./src/shaders/tile.frag");
    std::ifstream textVertFile("./src/shaders/text.vert");
    std::ifstream textFragFile("./src/shaders/text.frag");

    std::cout << "Shader files check:" << std::endl;
    std::cout << "tile.vert exists: " << (vertFile.good() ? "Yes" : "No") << std::endl;
    std::cout << "tile.frag exists: " << (fragFile.good() ? "Yes" : "No") << std::endl;
    std::cout << "text.vert exists: " << (textVertFile.good() ? "Yes" : "No") << std::endl;
    std::cout << "text.frag exists: " << (textFragFile.good() ? "Yes" : "No") << std::endl;

    // カレントディレクトリの確認
    char cwd[1024];
    if (_getcwd(cwd, sizeof(cwd)) != NULL) {
        std::cout << "Current working directory: " << cwd << std::endl;
    } else {
        std::cerr << "Failed to get current working directory" << std::endl;
    }

    // ImGuiのコンテキスト作成
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO();
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard; // キーボードコントロール有効

    // ImGuiスタイルの設定
    ImGui::StyleColorsDark();

    // ImGuiのバックエンド設定
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // 音声システムの初期化
    if (!g_audioSystem.initialize()) {
        std::cerr << "Warning: Failed to initialize audio system" << std::endl;
    }

    // 15パズルの初期化
    Puzzle puzzle;
    puzzle.shuffle(100); // 100回のランダムな移動でシャッフル

    // パズルレンダラーの初期化
    SimplePuzzleRenderer puzzleRenderer(WINDOW_WIDTH, WINDOW_HEIGHT);

    // ゲーム開始時刻を記録
    auto startTime = std::chrono::steady_clock::now();
    unsigned int lastSolvedSeed = 0; // 最後に解いた時のシードを保持する変数

    // メインループ
    while (!glfwWindowShouldClose(window)) {
        // イベント処理
        glfwPollEvents();

        // マウスクリック処理
        static bool prevMousePressed = false; // prevMousePressed は他のクリックイベント検知のために残しても良い
        static int lastEmptyRow = -1, lastEmptyCol = -1; // 最後の空きマスの位置を記録

        if (mousePressed && !io.WantCaptureMouse && !puzzle.isSolved()) {
            // マウスボタンが押されている間、常にタイルの移動を試みる
            int r, c;
            if (puzzleRenderer.getTileAtPosition(mouseX, mouseY, r, c)) {
                // 現在の空きマスの位置を取得
                int emptyRow = puzzle.getEmptyRow();
                int emptyCol = puzzle.getEmptyCol();

                // 空きマスの位置が変わった場合、または新しくマウスボタンが押された場合のみ移動を試みる
                if (emptyRow != lastEmptyRow || emptyCol != lastEmptyCol || !prevMousePressed) {
                    // タイルの値を取得
                    int tileValue = puzzle.getTile(r, c);

                    // タイルを移動
                    if (puzzle.moveTile(r, c)) {
                        // 移動が成功した場合、音を再生
                        if (g_audioSystem.isInitialized()) {
                            g_audioSystem.playWavFromMemory(g_move_wav_data, g_move_wav_data_len);
                        }

                        // 移動が成功した場合、アニメーションを開始
                        if (!puzzleRenderer.isAnimationRunning()) {
                            puzzleRenderer.startTileMoveAnimation(tileValue, r, c, emptyRow, emptyCol);
                        }

                        // 最後の空きマスの位置を更新
                        lastEmptyRow = emptyRow;
                        lastEmptyCol = emptyCol;
                    }
                }
            }
        } else if (!mousePressed) {
            // マウスボタンが離されたら、最後の空きマスの位置をリセット
            lastEmptyRow = -1;
            lastEmptyCol = -1;
        }

        prevMousePressed = mousePressed;

        // キーボード入力処理 (矢印キー)
        ImGuiIO& io_key = ImGui::GetIO(); // ImGuiIOを取得 (変数名を変更して衝突を回避)
        if (!io_key.WantCaptureKeyboard && !puzzle.isSolved() && !puzzleRenderer.isAnimationRunning()) { // アニメーション中は入力を無視
            int emptyR = puzzle.getEmptyRow();
            int emptyC = puzzle.getEmptyCol();

            if (ImGui::IsKeyPressed(ImGuiKey_UpArrow)) {
                if (emptyR < PUZZLE_SIZE - 1) { // 空きマスが最下段でない場合
                    int tileR = emptyR + 1;
                    int tileC = emptyC;
                    int tileValue = puzzle.getTile(tileR, tileC);
                    if (puzzle.moveTile(tileR, tileC)) { // 空きマスの下のタイルを上に移動
                        // 移動が成功した場合、音を再生
                        if (g_audioSystem.isInitialized()) {
                            g_audioSystem.playWavFromMemory(g_move_wav_data, g_move_wav_data_len);
                        }
                        puzzleRenderer.startTileMoveAnimation(tileValue, tileR, tileC, emptyR, emptyC);
                    }
                }
            }
            else if (ImGui::IsKeyPressed(ImGuiKey_DownArrow)) {
                if (emptyR > 0) { // 空きマスが最上段でない場合
                    int tileR = emptyR - 1;
                    int tileC = emptyC;
                    int tileValue = puzzle.getTile(tileR, tileC);
                    if (puzzle.moveTile(tileR, tileC)) { // 空きマスの上のタイルを下に移動
                        // 移動が成功した場合、音を再生
                        if (g_audioSystem.isInitialized()) {
                            g_audioSystem.playWavFromMemory(g_move_wav_data, g_move_wav_data_len);
                        }
                        puzzleRenderer.startTileMoveAnimation(tileValue, tileR, tileC, emptyR, emptyC);
                    }
                }
            }
            else if (ImGui::IsKeyPressed(ImGuiKey_LeftArrow)) {
                if (emptyC < PUZZLE_SIZE - 1) { // 空きマスが最右列でない場合
                    int tileR = emptyR;
                    int tileC = emptyC + 1;
                    int tileValue = puzzle.getTile(tileR, tileC);
                    if (puzzle.moveTile(tileR, tileC)) { // 空きマスの右のタイルを左に移動
                        // 移動が成功した場合、音を再生
                        if (g_audioSystem.isInitialized()) {
                            g_audioSystem.playWavFromMemory(g_move_wav_data, g_move_wav_data_len);
                        }
                        puzzleRenderer.startTileMoveAnimation(tileValue, tileR, tileC, emptyR, emptyC);
                    }
                }
            }
            else if (ImGui::IsKeyPressed(ImGuiKey_RightArrow)) {
                if (emptyC > 0) { // 空きマスが最左列でない場合
                    int tileR = emptyR;
                    int tileC = emptyC - 1;
                    int tileValue = puzzle.getTile(tileR, tileC);
                    if (puzzle.moveTile(tileR, tileC)) { // 空きマスの左のタイルを右に移動
                        // 移動が成功した場合、音を再生
                        if (g_audioSystem.isInitialized()) {
                            g_audioSystem.playWavFromMemory(g_move_wav_data, g_move_wav_data_len);
                        }
                        puzzleRenderer.startTileMoveAnimation(tileValue, tileR, tileC, emptyR, emptyC);
                    }
                }
            }
        }

        // ImGuiフレーム開始
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // ImGuiウィンドウ（UIのみ）
        ImGui::Begin("UI", nullptr);
        ImGui::SetWindowFontScale(1.0f); // 文字を少し大きくする

        // 移動回数と状態の表示
        bool isCurrentlySolved = puzzle.isSolved();
        if (isCurrentlySolved) {
            ImGui::Text("Puzzle Solved! Moves: %d", puzzle.getMoves());
            if (!wasPuzzleSolvedInPreviousFrame) {
                // パズルが今フレームで解かれた場合
                auto solvedTimePoint = std::chrono::steady_clock::now();
                finalSolvedDuration = solvedTimePoint - startTime; // 解けた時間を保存
                isTimerRunning = false; // タイマーを停止

                long long h_solved = std::chrono::duration_cast<std::chrono::hours>(finalSolvedDuration).count();
                long long m_solved = std::chrono::duration_cast<std::chrono::minutes>(finalSolvedDuration).count() % 60;
                long long s_solved = std::chrono::duration_cast<std::chrono::seconds>(finalSolvedDuration).count() % 60;
                long long ms_solved = std::chrono::duration_cast<std::chrono::milliseconds>(finalSolvedDuration).count() % 1000;
                long long us_solved = std::chrono::duration_cast<std::chrono::microseconds>(finalSolvedDuration).count() % 1000;

                std::ostringstream timeStream;
                timeStream << std::setfill('0') << std::setw(2) << h_solved << ":"
                           << std::setfill('0') << std::setw(2) << m_solved << ":"
                           << std::setfill('0') << std::setw(2) << s_solved << "."
                           << std::setfill('0') << std::setw(3) << ms_solved << "."
                           << std::setfill('0') << std::setw(3) << us_solved;

                unsigned int currentSeed = puzzle.getCurrentSeed();
                // シードが前回解いた時と同じ場合は記録しない（リセットなしで連続して解いたとみなす）
                // ただし、初回の記録や、シードが0（初期状態など）の場合は記録する
                if (currentSeed != lastSolvedSeed || solvedTimesLog.empty() || currentSeed == 0) {
                    std::string logEntry = "Time: " + timeStream.str() + " (Seed: " + std::to_string(currentSeed) + ")";
                    solvedTimesLog.insert(solvedTimesLog.begin(), logEntry); // 先頭に追加
                    lastSolvedSeed = currentSeed; // 今回解いたシードを保存
                }
            }
        } else {
            ImGui::Text("Moves: %d", puzzle.getMoves());
        }
        wasPuzzleSolvedInPreviousFrame = isCurrentlySolved; // 現在の解決状態を保存
        ImGui::Text("Current Seed: %u", puzzle.getCurrentSeed());

        // 経過時間の計算と表示
        std::chrono::steady_clock::duration currentDuration;
        if (isTimerRunning) {
            auto currentTime = std::chrono::steady_clock::now();
            currentDuration = currentTime - startTime;
        } else {
            currentDuration = finalSolvedDuration; // タイマーが止まっていれば最後に解いた時間を使用
        }

        long long h = std::chrono::duration_cast<std::chrono::hours>(currentDuration).count();
        long long m = std::chrono::duration_cast<std::chrono::minutes>(currentDuration).count() % 60;
        long long s = std::chrono::duration_cast<std::chrono::seconds>(currentDuration).count() % 60;
        long long ms_part = std::chrono::duration_cast<std::chrono::milliseconds>(currentDuration).count() % 1000;
        long long us_part = std::chrono::duration_cast<std::chrono::microseconds>(currentDuration).count() % 1000; // 「ナノ秒」の指定をマイクロ秒として解釈

        ImGui::Text("Time: %02lld:%02lld:%02lld.%03lld.%03lld", h, m, s, ms_part, us_part);

        // リセットボタン
        if (ImGui::Button("Reset", ImVec2(100, 30))) {
            // リセット前にアニメーションを開始
            if (!puzzleRenderer.isAnimationRunning()) {
                // 現在のパズル状態でリセットアニメーションを開始
                puzzleRenderer.startResetAnimation(puzzle);

                // アニメーション開始後にパズルをリセット
                puzzle.reset();
                puzzle.shuffle(100); // シードを指定しない場合は時刻ベース
                startTime = std::chrono::steady_clock::now(); // リセット時に開始時刻もリセット
                wasPuzzleSolvedInPreviousFrame = false; // リセット時は未解決状態に戻す
                isTimerRunning = true; // タイマーを再開
                // lastSolvedSeed はリセットボタンでは変更しない（次のシャッフルで新しいシードが設定されるため）
            }
        }

        ImGui::End();

        // 解けた時間の履歴
        ImGui::Begin("Solved Times", nullptr);
        if (ImGui::Button("Clear Log")) {
            solvedTimesLog.clear();
            lastSolvedSeed = 0; // ログクリア時に最後に解いたシードもリセット
        }
        ImGui::Separator();
        for (const auto& log : solvedTimesLog) {
            ImGui::TextUnformatted(log.c_str());
        }
        ImGui::End();

        // レンダリング
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);
        glViewport(0, 0, display_w, display_h);
        glClearColor(34.0f / 255.0f, 34.0f / 255.0f, 34.0f / 255.0f, 1.0f); // 背景色変更
        glClear(GL_COLOR_BUFFER_BIT);

        // パズルの描画（OpenGL）
        puzzleRenderer.render(puzzle, mouseX, mouseY, static_cast<float>(glfwGetTime())); // glfwGetTime() を追加

        // ImGuiの描画（UI）
        ImGui::Render();
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

        glfwSwapBuffers(window);
    }

    // クリーンアップ
    g_audioSystem.shutdown();

    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}