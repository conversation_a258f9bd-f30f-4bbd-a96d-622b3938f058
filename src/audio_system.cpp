#include "../include/audio_system.h"
#include <iostream>
#include <cstring>
#include <fstream>
#include <memory>

// グローバルインスタンス
AudioSystem g_audioSystem;

AudioSystem::AudioSystem() : initialized(false) {
#ifdef __linux__
    pulseAudio = nullptr;
    sampleSpec.format = PA_SAMPLE_S16LE;
    sampleSpec.channels = 2;
    sampleSpec.rate = 44100;
#endif
}

AudioSystem::~AudioSystem() {
    shutdown();
}

bool AudioSystem::initialize() {
    if (initialized) {
        return true;
    }

#ifdef _WIN32
    // Windows環境では特別な初期化は不要
    initialized = true;
    std::cout << "Audio system initialized (Windows)" << std::endl;
    return true;
#elif defined(__linux__)
    // PulseAudioの初期化
    int error;
    pulseAudio = pa_simple_new(
        nullptr,                // サーバー名（デフォルト）
        "15Puzzle-C",          // アプリケーション名
        PA_STREAM_PLAYBACK,    // 再生ストリーム
        nullptr,               // デバイス名（デフォルト）
        "Game Audio",          // ストリーム説明
        &sampleSpec,           // サンプル仕様
        nullptr,               // チャンネルマップ（デフォルト）
        nullptr,               // バッファ属性（デフォルト）
        &error                 // エラーコード
    );

    if (!pulseAudio) {
        std::cerr << "Failed to initialize PulseAudio: " << pa_strerror(error) << std::endl;
        return false;
    }

    initialized = true;
    std::cout << "Audio system initialized (Linux/PulseAudio)" << std::endl;
    return true;
#else
    std::cerr << "Audio system not supported on this platform" << std::endl;
    return false;
#endif
}

void AudioSystem::shutdown() {
    if (!initialized) {
        return;
    }

    stopAll();

#ifdef __linux__
    if (pulseAudio) {
        pa_simple_free(pulseAudio);
        pulseAudio = nullptr;
    }
#endif

    initialized = false;
    std::cout << "Audio system shutdown" << std::endl;
}

bool AudioSystem::playWavFromMemory(const unsigned char* wavData, size_t dataSize, bool loop) {
    if (!initialized || !wavData || dataSize == 0) {
        return false;
    }

#ifdef _WIN32
    // 前の音声を停止
    stopAll();

    // WAVデータをコピー
    currentWavData.data = std::make_unique<unsigned char[]>(dataSize);
    std::memcpy(currentWavData.data.get(), wavData, dataSize);
    currentWavData.size = dataSize;

    // PlaySoundを使用してメモリから再生
    DWORD flags = SND_MEMORY | SND_ASYNC;
    if (loop) {
        flags |= SND_LOOP;
    }

    BOOL result = PlaySound(
        reinterpret_cast<LPCSTR>(currentWavData.data.get()),
        nullptr,
        flags
    );

    if (!result) {
        std::cerr << "Failed to play WAV from memory" << std::endl;
        return false;
    }

    return true;
#elif defined(__linux__)
    // 前の音声を停止
    stopAll();

    // WAVヘッダーの解析
    if (!parseWavHeader(wavData, dataSize, currentWavData)) {
        std::cerr << "Failed to parse WAV header" << std::endl;
        return false;
    }

    // WAVデータをコピー
    currentWavData.data = std::make_unique<unsigned char[]>(dataSize);
    std::memcpy(currentWavData.data.get(), wavData, dataSize);
    currentWavData.size = dataSize;

    // PulseAudioで再生（簡易実装）
    if (pulseAudio) {
        // WAVヘッダーをスキップしてPCMデータのみを再生
        const unsigned char* pcmData = wavData + 44; // 標準的なWAVヘッダーサイズ
        size_t pcmSize = dataSize - 44;

        int error;
        if (pa_simple_write(pulseAudio, pcmData, pcmSize, &error) < 0) {
            std::cerr << "Failed to write audio data: " << pa_strerror(error) << std::endl;
            return false;
        }

        if (pa_simple_drain(pulseAudio, &error) < 0) {
            std::cerr << "Failed to drain audio: " << pa_strerror(error) << std::endl;
            return false;
        }
    }

    return true;
#else
    std::cerr << "playWavFromMemory not supported on this platform" << std::endl;
    return false;
#endif
}

bool AudioSystem::playWavFromFile(const std::string& filename, bool loop) {
    if (!initialized) {
        return false;
    }

#ifdef _WIN32
    // 前の音声を停止
    stopAll();

    DWORD flags = SND_FILENAME | SND_ASYNC;
    if (loop) {
        flags |= SND_LOOP;
    }

    BOOL result = PlaySound(filename.c_str(), nullptr, flags);

    if (!result) {
        std::cerr << "Failed to play WAV file: " << filename << std::endl;
        return false;
    }

    return true;
#elif defined(__linux__)
    // ファイルからの再生はメモリ再生と同様の処理
    // 実際の実装では、ファイルを読み込んでplayWavFromMemoryを呼び出す
    std::ifstream file(filename, std::ios::binary);
    if (!file) {
        std::cerr << "Failed to open WAV file: " << filename << std::endl;
        return false;
    }

    // ファイルサイズを取得
    file.seekg(0, std::ios::end);
    size_t fileSize = file.tellg();
    file.seekg(0, std::ios::beg);

    // ファイルデータを読み込み
    auto fileData = std::make_unique<unsigned char[]>(fileSize);
    file.read(reinterpret_cast<char*>(fileData.get()), fileSize);
    file.close();

    // メモリ再生を呼び出し
    return playWavFromMemory(fileData.get(), fileSize, loop);
#else
    std::cerr << "playWavFromFile not supported on this platform" << std::endl;
    return false;
#endif
}

void AudioSystem::stopAll() {
#ifdef _WIN32
    PlaySound(nullptr, nullptr, SND_PURGE);
    currentWavData.data.reset();
    currentWavData.size = 0;
#elif defined(__linux__)
    // Linux環境では現在再生中の音声を停止する簡易実装
    currentWavData.data.reset();
    currentWavData.size = 0;
    // 注意: PulseAudioでは再生中の音声を途中で停止するのは複雑
    // より高度な実装が必要な場合は、別のスレッドで再生管理を行う
#endif
}

#ifdef __linux__
bool AudioSystem::parseWavHeader(const unsigned char* data, size_t size, WavData& wavData) {
    if (size < 44) {
        return false; // WAVヘッダーが不完全
    }

    // WAVヘッダーの基本的な検証
    if (std::memcmp(data, "RIFF", 4) != 0 || std::memcmp(data + 8, "WAVE", 4) != 0) {
        return false; // WAVファイルではない
    }

    // サンプルレート、チャンネル数、ビット深度を取得
    wavData.sampleRate = *reinterpret_cast<const uint32_t*>(data + 24);
    wavData.channels = *reinterpret_cast<const uint16_t*>(data + 22);
    wavData.bitsPerSample = *reinterpret_cast<const uint16_t*>(data + 34);

    return true;
}
#endif
