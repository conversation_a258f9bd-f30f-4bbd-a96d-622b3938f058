#include "../include/audio_system.h"
#include <iostream>
#include <cstring>

// グローバルインスタンス
AudioSystem g_audioSystem;

AudioSystem::AudioSystem() : initialized(false) {
}

AudioSystem::~AudioSystem() {
    shutdown();
}

bool AudioSystem::initialize() {
    if (initialized) {
        return true;
    }

#ifdef _WIN32
    // Windows環境では特別な初期化は不要
    initialized = true;
    std::cout << "Audio system initialized (Windows)" << std::endl;
    return true;
#else
    std::cerr << "Audio system not supported on this platform" << std::endl;
    return false;
#endif
}

void AudioSystem::shutdown() {
    if (!initialized) {
        return;
    }

    stopAll();
    initialized = false;
    std::cout << "Audio system shutdown" << std::endl;
}

bool AudioSystem::playWavFromMemory(const unsigned char* wavData, size_t dataSize, bool loop) {
    if (!initialized || !wavData || dataSize == 0) {
        return false;
    }

#ifdef _WIN32
    // 前の音声を停止
    stopAll();

    // WAVデータをコピー
    currentWavData.data = std::make_unique<unsigned char[]>(dataSize);
    std::memcpy(currentWavData.data.get(), wavData, dataSize);
    currentWavData.size = dataSize;

    // PlaySoundを使用してメモリから再生
    DWORD flags = SND_MEMORY | SND_ASYNC;
    if (loop) {
        flags |= SND_LOOP;
    }

    BOOL result = PlaySound(
        reinterpret_cast<LPCSTR>(currentWavData.data.get()),
        nullptr,
        flags
    );

    if (!result) {
        std::cerr << "Failed to play WAV from memory" << std::endl;
        return false;
    }

    return true;
#else
    std::cerr << "playWavFromMemory not supported on this platform" << std::endl;
    return false;
#endif
}

bool AudioSystem::playWavFromFile(const std::string& filename, bool loop) {
    if (!initialized) {
        return false;
    }

#ifdef _WIN32
    // 前の音声を停止
    stopAll();

    DWORD flags = SND_FILENAME | SND_ASYNC;
    if (loop) {
        flags |= SND_LOOP;
    }

    BOOL result = PlaySound(filename.c_str(), nullptr, flags);

    if (!result) {
        std::cerr << "Failed to play WAV file: " << filename << std::endl;
        return false;
    }

    return true;
#else
    std::cerr << "playWavFromFile not supported on this platform" << std::endl;
    return false;
#endif
}

void AudioSystem::stopAll() {
#ifdef _WIN32
    PlaySound(nullptr, nullptr, SND_PURGE);
    currentWavData.data.reset();
    currentWavData.size = 0;
#endif
}
