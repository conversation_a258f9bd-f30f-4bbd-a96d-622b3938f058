#include "../include/puzzle_renderer.h"
#include <iostream>

PuzzleRenderer::PuzzleRenderer(unsigned int width, unsigned int height)
    : width(width), height(height), tileSize(80.0f), tileSpacing(5.0f),
      normalColor(glm::vec3(0.33f, 0.33f, 0.66f)),
      hoverColor(glm::vec3(0.47f, 0.47f, 0.8f)),
      solvedColor(glm::vec3(0.33f, 0.66f, 0.33f)) {

    // 描画開始位置の計算
    startX = (width - (PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing)) / 2.0f;
    startY = (height - (PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing)) / 2.0f;

    // シェーダーの読み込み
    try {
        tileShader = new Shader("./src/shaders/tile.vert", "./src/shaders/tile.frag");
        std::cout << "Tile shader loaded successfully" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Failed to load tile shader: " << e.what() << std::endl;
    }

    // テキストレンダラーの初期化
    textRenderer = new TextRenderer(width, height);
    try {
        textRenderer->Load("C:/Windows/Fonts/meiryo.ttc", 24); // 日本語フォントを使用
        std::cout << "Font loaded successfully" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Failed to load font: " << e.what() << std::endl;
        try {
            textRenderer->Load("C:/Windows/Fonts/arial.ttf", 24); // 代替フォント
            std::cout << "Alternative font loaded successfully" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Failed to load alternative font: " << e.what() << std::endl;
        }
    }

    // 初期化
    initialize();
}

PuzzleRenderer::~PuzzleRenderer() {
    // リソースの解放
    glDeleteVertexArrays(1, &VAO);
    glDeleteBuffers(1, &VBO);
    glDeleteBuffers(1, &EBO);
    delete tileShader;
    delete textRenderer;
}

void PuzzleRenderer::initialize() {
    // 頂点データの作成（単一の正方形）
    vertices = {
        // 位置(x, y), 色(r, g, b)
        0.0f, 0.0f, 1.0f, 1.0f, 1.0f,
        1.0f, 0.0f, 1.0f, 1.0f, 1.0f,
        1.0f, 1.0f, 1.0f, 1.0f, 1.0f,
        0.0f, 1.0f, 1.0f, 1.0f, 1.0f
    };

    // インデックスデータ
    indices = {
        0, 1, 2,
        2, 3, 0
    };

    // VAO, VBO, EBOの設定
    glGenVertexArrays(1, &VAO);
    glGenBuffers(1, &VBO);
    glGenBuffers(1, &EBO);

    glBindVertexArray(VAO);

    glBindBuffer(GL_ARRAY_BUFFER, VBO);
    glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(float), vertices.data(), GL_STATIC_DRAW);

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, EBO);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(unsigned int), indices.data(), GL_STATIC_DRAW);

    // 位置属性
    glVertexAttribPointer(0, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);

    // 色属性
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(2 * sizeof(float)));
    glEnableVertexAttribArray(1);

    glBindBuffer(GL_ARRAY_BUFFER, 0);
    glBindVertexArray(0);

    // 射影行列の設定
    glm::mat4 projection = glm::ortho(0.0f, static_cast<float>(width), static_cast<float>(height), 0.0f, -1.0f, 1.0f);
    tileShader->use();
    tileShader->setMat4("projection", projection);
}

glm::vec2 PuzzleRenderer::getTilePosition(int row, int col) {
    float x = startX + col * (tileSize + tileSpacing);
    float y = startY + row * (tileSize + tileSpacing);
    return glm::vec2(x, y);
}

bool PuzzleRenderer::getTileAtPosition(double mouseX, double mouseY, int& row, int& col) {
    // マウス座標がパズル領域内かチェック
    if (mouseX < startX || mouseX > startX + PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing ||
        mouseY < startY || mouseY > startY + PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing) {
        return false;
    }

    // タイルの行と列を計算
    float relX = mouseX - startX;
    float relY = mouseY - startY;

    col = static_cast<int>(relX / (tileSize + tileSpacing));
    row = static_cast<int>(relY / (tileSize + tileSpacing));

    // タイル内の位置をチェック（タイル間のスペースをクリックした場合は無視）
    float tileX = relX - col * (tileSize + tileSpacing);
    float tileY = relY - row * (tileSize + tileSpacing);

    if (tileX > tileSize || tileY > tileSize) {
        return false;
    }

    return (row >= 0 && row < PUZZLE_SIZE && col >= 0 && col < PUZZLE_SIZE);
}

void PuzzleRenderer::render(Puzzle& puzzle, double mouseX, double mouseY) {
    // シェーダーの有効化
    tileShader->use();

    // VAOのバインド
    glBindVertexArray(VAO);

    // ホバー状態のタイルを追跡
    int hoverRow = -1, hoverCol = -1;
    getTileAtPosition(mouseX, mouseY, hoverRow, hoverCol);

    // タイルの描画
    for (int row = 0; row < PUZZLE_SIZE; row++) {
        for (int col = 0; col < PUZZLE_SIZE; col++) {
            int tileValue = puzzle.getTile(row, col);
            if (tileValue == 0) continue; // 空きスペースはスキップ

            // タイルの位置
            glm::vec2 pos = getTilePosition(row, col);

            // モデル行列の設定
            glm::mat4 model = glm::mat4(1.0f);
            model = glm::translate(model, glm::vec3(pos.x, pos.y, 0.0f));
            model = glm::scale(model, glm::vec3(tileSize, tileSize, 1.0f));
            tileShader->setMat4("model", model);

            // タイルの色を設定
            glm::vec3 color;
            if (puzzle.isSolved()) {
                color = solvedColor;
            } else if (row == hoverRow && col == hoverCol) {
                color = hoverColor;
            } else {
                color = normalColor;
            }

            // 頂点の色を更新
            for (int i = 0; i < 4; i++) {
                vertices[i * 5 + 2] = color.r;
                vertices[i * 5 + 3] = color.g;
                vertices[i * 5 + 4] = color.b;
            }

            glBindBuffer(GL_ARRAY_BUFFER, VBO);
            glBufferSubData(GL_ARRAY_BUFFER, 0, vertices.size() * sizeof(float), vertices.data());

            // タイルを描画
            glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, 0);

            // タイルの数字を描画
            std::string tileText = std::to_string(tileValue);
            float textWidth = tileText.length() * 14.0f * 0.5f; // 簡易的な幅計算
            float textX = pos.x + (tileSize - textWidth) / 2.0f;
            float textY = pos.y + tileSize / 2.0f - 12.0f;
            textRenderer->RenderText(tileText, textX, textY, 1.0f, glm::vec3(1.0f, 1.0f, 1.0f));
        }
    }

    // VAOのバインド解除
    glBindVertexArray(0);
}

bool PuzzleRenderer::handleMouseClick(Puzzle& puzzle, double mouseX, double mouseY) {
    int row, col;
    if (getTileAtPosition(mouseX, mouseY, row, col)) {
        return puzzle.moveTile(row, col);
    }
    return false;
}
