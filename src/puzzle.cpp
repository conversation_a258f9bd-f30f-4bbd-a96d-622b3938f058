#include "../include/puzzle.h"
#include <cstdlib>
#include <ctime>
#include <cmath>
#include <random> // <random> をインクルード

// コンストラクタ - パズルの初期化
Puzzle::Puzzle() {
    reset();
}

// パズルをリセット（初期状態に戻す）
void Puzzle::reset() {
    int value = 1;

    // 盤面を初期化（1から15までの数字を順番に配置）
    for (int row = 0; row < PUZZLE_SIZE; row++) {
        for (int col = 0; col < PUZZLE_SIZE; col++) {
            if (row == PUZZLE_SIZE - 1 && col == PUZZLE_SIZE - 1) {
                board[row][col] = 0;  // 右下を空きスペースに
            } else {
                board[row][col] = value++;
            }
        }
    }

    // 空きスペースの位置を設定
    emptyRow = PUZZLE_SIZE - 1;
    emptyCol = PUZZLE_SIZE - 1;

    // その他の状態を初期化
    moves = 0;
    solved = true;  // 初期状態は解決済み
}

// パズルをシャッフル
void Puzzle::shuffle(int moves, unsigned int seed) {
    // 乱数生成器の初期化
    if (seed == 0) {
        std::random_device rd; // 非決定論的な乱数生成器
        this->currentSeed = rd(); // これをシードとして使用
    } else {
        this->currentSeed = seed;
    }
    std::srand(this->currentSeed);

    // 指定された回数ランダムに動かす
    for (int i = 0; i < moves; i++) {
        int direction = std::rand() % 4;  // 0: 上, 1: 右, 2: 下, 3: 左
        int newRow = emptyRow;
        int newCol = emptyCol;

        // 方向に基づいて新しい位置を計算
        switch (direction) {
            case 0: newRow--; break;  // 上
            case 1: newCol++; break;  // 右
            case 2: newRow++; break;  // 下
            case 3: newCol--; break;  // 左
        }

        // 盤面の範囲内かチェック
        if (newRow >= 0 && newRow < PUZZLE_SIZE && newCol >= 0 && newCol < PUZZLE_SIZE) {
            // タイルを移動
            board[emptyRow][emptyCol] = board[newRow][newCol];
            board[newRow][newCol] = 0;
            emptyRow = newRow;
            emptyCol = newCol;
        }
    }

    // 移動回数をリセット
    this->moves = 0;

    // 解決状態をチェック
    solved = checkSolved();
}

// タイルを移動（行と列の位置を指定）
bool Puzzle::moveTile(int row, int col) {
    // 指定された位置が盤面の範囲内かチェック
    if (row < 0 || row >= PUZZLE_SIZE || col < 0 || col >= PUZZLE_SIZE) {
        return false;
    }

    // 指定された位置が空きスペースに隣接しているかチェック
    if ((std::abs(row - emptyRow) == 1 && col == emptyCol) ||
        (std::abs(col - emptyCol) == 1 && row == emptyRow)) {

        // タイルを移動
        board[emptyRow][emptyCol] = board[row][col];
        board[row][col] = 0;
        emptyRow = row;
        emptyCol = col;

        // 移動回数を増やす
        moves++;

        // 解決状態をチェック
        solved = checkSolved();

        return true;
    }

    return false;
}

// パズルが解決されたかチェック
bool Puzzle::checkSolved() {
    int value = 1;

    for (int row = 0; row < PUZZLE_SIZE; row++) {
        for (int col = 0; col < PUZZLE_SIZE; col++) {
            // 右下のマス以外をチェック
            if (row != PUZZLE_SIZE - 1 || col != PUZZLE_SIZE - 1) {
                if (board[row][col] != value++) {
                    return false;
                }
            } else {
                // 右下は空きスペース（0）であるべき
                if (board[row][col] != 0) {
                    return false;
                }
            }
        }
    }

    return true;
}

// 特定のタイルが正しい位置にあるかチェック
bool Puzzle::isTileCorrect(int row, int col) const {
    if (row < 0 || row >= PUZZLE_SIZE || col < 0 || col >= PUZZLE_SIZE) {
        return false; // 範囲外
    }
    int expectedValue = row * PUZZLE_SIZE + col + 1;
    if (row == PUZZLE_SIZE - 1 && col == PUZZLE_SIZE - 1) {
        expectedValue = 0; // 最後のマスは空きスペース
    }
    return board[row][col] == expectedValue;
}
