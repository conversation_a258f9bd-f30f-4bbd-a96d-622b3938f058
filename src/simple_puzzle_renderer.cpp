#define _USE_MATH_DEFINES // For M_PI on Windows/MSVC, place before <cmath>
#include <cmath>          // For cos, sin, M_PI
#include <GL/glew.h>    // GLEWをGLFWより先にインクルード
#include <GLFW/glfw3.h> // Added for glfwGetTime()

#ifndef M_PI
    #define M_PI 3.14159265358979323846 // Define M_PI if not already defined
#endif

#include "../include/simple_puzzle_renderer.h"
#include <iostream>
#include <glm/gtc/type_ptr.hpp>
#include <iostream> // For debugging, if needed

SimplePuzzleRenderer::SimplePuzzleRenderer(unsigned int width, unsigned int height)
    : width(width), height(height), tileSize(80.0f), tileSpacing(5.0f),
      normalColor(glm::vec3(0.33f, 0.33f, 0.66f)),
      hoverColor(glm::vec3(0.47f, 0.47f, 0.8f)),
      solvedColor(glm::vec3(0.33f, 0.66f, 0.33f)),
      movingTileValue(0), isResetAnimation(false), animationStartTime(0.0f) { // Initialize new members

    // 描画開始位置の計算
    startX = (width - (PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing)) / 2.0f;
    startY = (height - (PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing)) / 2.0f;

    // 初期化
    initialize();
}

SimplePuzzleRenderer::~SimplePuzzleRenderer() {
    // OpenGL 1.x/2.x スタイルでは特別なリソース解放は不要
    std::cout << "SimplePuzzleRenderer destroyed" << std::endl;
}

void SimplePuzzleRenderer::initialize() {
    // OpenGL 1.x/2.x スタイルでは VAO/VBO は不要
    // 代わりに直接 glBegin/glEnd を使用

    // デバッグ出力
    std::cout << "SimplePuzzleRenderer initialized" << std::endl;
    std::cout << "Window size: " << width << "x" << height << std::endl;
    std::cout << "Puzzle start position: " << startX << ", " << startY << std::endl;
    std::cout << "Tile size: " << tileSize << ", spacing: " << tileSpacing << std::endl;
}

bool SimplePuzzleRenderer::getTileAtPosition(double mouseX, double mouseY, int& row, int& col) const { // Added const
    // マウス座標がパズル領域内かチェック
    if (mouseX < startX || mouseX > startX + PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing ||
        mouseY < startY || mouseY > startY + PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing) {
        return false;
    }

    // タイルの行と列を計算
    float relX = mouseX - startX;
    float relY = mouseY - startY;

    col = static_cast<int>(relX / (tileSize + tileSpacing));
    row = static_cast<int>(relY / (tileSize + tileSpacing));

    // タイル内の位置をチェック（タイル間のスペースをクリックした場合は無視）
    float tileX = relX - col * (tileSize + tileSpacing);
    float tileY = relY - row * (tileSize + tileSpacing);

    if (tileX > tileSize || tileY > tileSize) {
        return false;
    }

    return (row >= 0 && row < PUZZLE_SIZE && col >= 0 && col < PUZZLE_SIZE);
}

void SimplePuzzleRenderer::drawTile(float x, float y, int value, bool isHovered, bool isCorrectlyPlaced) {
    bool isEmpty = (value == 0);
    float currentTileSize = this->tileSize;

    if (isEmpty) {
        glColor3f(17.0f / 255.0f, 17.0f / 255.0f, 17.0f / 255.0f); // Empty tile color (remains the same)
        glDisable(GL_TEXTURE_2D);
    } else {
        if (isCorrectlyPlaced) { // Tile is in the correct position
            glColor3f(238.0f / 255.0f, 238.0f / 255.0f, 238.0f / 255.0f); // Default tile background
        } else { // Tile is NOT in the correct position
            glColor3f(17.0f / 255.0f, 17.0f / 255.0f, 17.0f / 255.0f); // Inverted background for incorrectly placed tiles
        }
        glDisable(GL_TEXTURE_2D);
    }

    const float r = currentTileSize * 0.1f;
    const int num_segments = 10;

    glBegin(GL_POLYGON);

    auto draw_vertex_with_tex = [&](float vx, float vy) {
        // テクスチャ座標の指定は不要になったため、glVertex2fのみ呼び出す
        // if (!isEmpty) {
        //     float u = (vx - x) / currentTileSize;
        //     float v = 1.0f - (vy - y) / currentTileSize;
        //     glTexCoord2f(u, v);
        // }
        glVertex2f(vx, vy);
    };

    // Top-left corner
    for (int i = 0; i <= num_segments; ++i) {
        float angle = M_PI - (M_PI / 2.0f) * (static_cast<float>(i) / static_cast<float>(num_segments));
        draw_vertex_with_tex(x + r + cos(angle) * r, y + r - sin(angle) * r);
    }

    // Top-right corner
    for (int i = 0; i <= num_segments; ++i) {
        float angle = (M_PI / 2.0f) - (M_PI / 2.0f) * (static_cast<float>(i) / static_cast<float>(num_segments));
        draw_vertex_with_tex(x + currentTileSize - r + cos(angle) * r, y + r - sin(angle) * r);
    }

    // Bottom-right corner
    for (int i = 0; i <= num_segments; ++i) {
        float angle = 0.0f - (M_PI / 2.0f) * (static_cast<float>(i) / static_cast<float>(num_segments));
        draw_vertex_with_tex(x + currentTileSize - r + cos(angle) * r, y + currentTileSize - r - sin(angle) * r);
    }

    // Bottom-left corner
    for (int i = 0; i <= num_segments; ++i) {
        float angle = (-M_PI / 2.0f) - (M_PI / 2.0f) * (static_cast<float>(i) / static_cast<float>(num_segments));
        draw_vertex_with_tex(x + r + cos(angle) * r, y + currentTileSize - r - sin(angle) * r);
    }

    glEnd();

    // if (!isEmpty) { // このブロックはテクスチャを使用しないので不要
    //     glDisable(GL_TEXTURE_2D);
    // }

    // Reset color to white, good practice if other things are drawn later without setting color
    // glColor3f(1.0f, 1.0f, 1.0f); // Resetting to white might interfere, removing for now
}

void SimplePuzzleRenderer::drawNumber(float x, float y, int number, bool isCorrectlyPlaced) {
    // Convert number to string
    std::string numStr = std::to_string(number);

    // Number color
    if (isCorrectlyPlaced) { // Tile is in the correct position
        glColor3f(17.0f / 255.0f, 17.0f / 255.0f, 17.0f / 255.0f); // Default text color
    } else { // Tile is NOT in the correct position
        glColor3f(238.0f / 255.0f, 238.0f / 255.0f, 238.0f / 255.0f); // Inverted text color for incorrectly placed tiles
    }

    // 数字の大きさ
    float baseSize = tileSize * 0.35f; // ベースサイズを少し小さくして2桁表示に対応
    float segmentLength = baseSize / 2.0f;
    float lineWidth = baseSize / 10.0f; // 線を少し太くする

    // 数字の描画（線分で描画）

    // 7セグメントディスプレイのように数字を描画
    auto drawDigit = [&](float currentX, float currentY, int digit) {
        glLineWidth(lineWidth);
        auto drawSegment = [&](bool top, bool topRight, bool bottomRight, bool bottom, bool bottomLeft, bool topLeft, bool middle) {
            if (top) {
                glBegin(GL_LINES);
                glVertex2f(currentX - segmentLength/2, currentY - segmentLength);
                glVertex2f(currentX + segmentLength/2, currentY - segmentLength);
                glEnd();
            }
            if (topRight) {
                glBegin(GL_LINES);
                glVertex2f(currentX + segmentLength/2, currentY - segmentLength);
                glVertex2f(currentX + segmentLength/2, currentY);
                glEnd();
            }
            if (bottomRight) {
                glBegin(GL_LINES);
                glVertex2f(currentX + segmentLength/2, currentY);
                glVertex2f(currentX + segmentLength/2, currentY + segmentLength);
                glEnd();
            }
            if (bottom) {
                glBegin(GL_LINES);
                glVertex2f(currentX - segmentLength/2, currentY + segmentLength);
                glVertex2f(currentX + segmentLength/2, currentY + segmentLength);
                glEnd();
            }
            if (bottomLeft) {
                glBegin(GL_LINES);
                glVertex2f(currentX - segmentLength/2, currentY);
                glVertex2f(currentX - segmentLength/2, currentY + segmentLength);
                glEnd();
            }
            if (topLeft) {
                glBegin(GL_LINES);
                glVertex2f(currentX - segmentLength/2, currentY - segmentLength);
                glVertex2f(currentX - segmentLength/2, currentY);
                glEnd();
            }
            if (middle) {
                glBegin(GL_LINES);
                glVertex2f(currentX - segmentLength/2, currentY);
                glVertex2f(currentX + segmentLength/2, currentY);
                glEnd();
            }
        };

        switch (digit) {
            case 0: drawSegment(true, true, true, true, true, true, false); break;
            case 1: drawSegment(false, true, true, false, false, false, false); break;
            case 2: drawSegment(true, true, false, true, true, false, true); break;
            case 3: drawSegment(true, true, true, true, false, false, true); break;
            case 4: drawSegment(false, true, true, false, false, true, true); break;
            case 5: drawSegment(true, false, true, true, false, true, true); break;
            case 6: drawSegment(true, false, true, true, true, true, true); break;
            case 7: drawSegment(true, true, true, false, false, false, false); break;
            case 8: drawSegment(true, true, true, true, true, true, true); break;
            case 9: drawSegment(true, true, true, true, false, true, true); break;
        }
    };

    if (number < 10) {
        // 1桁の数字
        drawDigit(x, y, number);
    } else {
        // 2桁の数字
        int tensDigit = number / 10;
        int onesDigit = number % 10;

        float digitSpacing = segmentLength * 0.75f; // 数字間のスペースを調整

        // 10の位を描画 (左側)
        drawDigit(x - digitSpacing, y, tensDigit);
        // 1の位を描画 (右側)
        drawDigit(x + digitSpacing, y, onesDigit);
    }

    glPopMatrix();
}

// render method signature updated to match header
// THIS OLD RENDER FUNCTION WILL BE REMOVED
/*
void SimplePuzzleRenderer::render(Puzzle& puzzle, double mouseX, double mouseY, float currentTime) { // Original line: void SimplePuzzleRenderer::render(Puzzle& puzzle, double mouseX, double mouseY) {
    // デバッグ出力
    // std::cout << "Rendering puzzle... Mouse: " << mouseX << ", " << mouseY << std::endl;

    // 2D描画の設定
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    glOrtho(0, width, height, 0, -1, 1);
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();

    // 描画状態の設定
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    // ホバー状態のタイルを追跡
    int hoverRow = -1, hoverCol = -1;
    getTileAtPosition(mouseX, mouseY, hoverRow, hoverCol);

    // Update animation state if a tile is moving
    if (movingTileValue != 0) {
        float elapsedTime = currentTime - animationStartTime; // Use currentTime passed to render
        if (elapsedTime < TILE_MOVE_DURATION) {
            float t = elapsedTime / TILE_MOVE_DURATION;
            float easedT = easeInOutQuad(t);
            movingTileCurrentScreenPos = glm::mix(movingTileStartScreenPos, movingTileTargetScreenPos, easedT); // Update current position
        } else {
            movingTileCurrentScreenPos = movingTileTargetScreenPos; // Snap to target
            movingTileValue = 0; // Animation finished
        }
    }

    // タイルの描画
    for (int row = 0; row < PUZZLE_SIZE; row++) {
        for (int col = 0; col < PUZZLE_SIZE; col++) {
            int tileValue = puzzle.getTile(row, col);
            // if (tileValue == 0) continue; // 空きスペースはスキップ // Allow drawing empty space if needed by new render

            // タイルの位置
            float x = startX + col * (tileSize + tileSpacing);
            float y = startY + row * (tileSize + tileSpacing);

            // Check if the tile is correctly placed
            bool isCorrectlyPlaced = false;
            if (tileValue != 0) { // Only check non-empty tiles
                isCorrectlyPlaced = puzzle.isTileCorrect(row, col);
            }

            // タイルを描画
            bool isHovered = (row == hoverRow && col == hoverCol);
            // The old drawTile signature is different from the one used in the new render function.
            // This old render function is being removed, so this part is less critical,
            // but for completeness, it would need to be adapted if kept.
            drawTile(x, y, tileValue, isHovered, isCorrectlyPlaced);


            // 数字の描画 (空のタイル以外)
            if (tileValue != 0) {
                // 数字をタイルの中央に描画
                float numberX = x + tileSize / 2.0f;
                float numberY = y + tileSize / 2.0f;
                // Ensure texturing is disabled before drawing numbers if numbers are not textured
                glDisable(GL_TEXTURE_2D);
                // The old drawNumber signature is also different.
                drawNumber(numberX, numberY, tileValue, isCorrectlyPlaced);
            }

            // デバッグ: タイルの位置を出力
            // std::cout << "Tile " << tileValue << " at (" << x << ", " << y << ")" << std::endl;
        }
    }
}
*/

bool SimplePuzzleRenderer::handleMouseClick(Puzzle& puzzle, double mouseX, double mouseY) {
    int row, col;
    if (getTileAtPosition(mouseX, mouseY, row, col)) {
        // タイルの移動前に空きマスの位置を取得
        int emptyRow = puzzle.getEmptyRow();
        int emptyCol = puzzle.getEmptyCol();

        // タイルの値を取得
        int tileValue = puzzle.getTile(row, col);

        // タイルを移動
        bool moved = puzzle.moveTile(row, col);

        // 移動が成功した場合、アニメーションを開始
        if (moved) {
            // アニメーションが既に実行中でなければ開始
            if (!isAnimationRunning()) {
                startTileMoveAnimation(tileValue, row, col, emptyRow, emptyCol);
            }
        }

        return moved;
    }
    return false;
}

// Easing functions (can be moved to a utility header if used elsewhere)
float SimplePuzzleRenderer::easeInOutQuad(float t) const {
    return t < 0.5f ? 2.0f * t * t : 1.0f - std::pow(-2.0f * t + 2.0f, 2.0f) / 2.0f;
}

float SimplePuzzleRenderer::easeInOutCubic(float t) const {
    return t < 0.5f ? 4.0f * t * t * t : 1.0f - std::pow(-2.0f * t + 2.0f, 3.0f) / 2.0f;
}

float SimplePuzzleRenderer::easeInOutQuart(float t) const {
    return t < 0.5f ? 8.0f * t * t * t * t : 1.0f - std::pow(-2.0f * t + 2.0f, 4.0f) / 2.0f;
}

float SimplePuzzleRenderer::easeOutBounce(float t) const {
    const float n1 = 7.5625f;
    const float d1 = 2.75f;

    if (t < 1.0f / d1) {
        return n1 * t * t;
    } else if (t < 2.0f / d1) {
        t -= 1.5f / d1;
        return n1 * t * t + 0.75f;
    } else if (t < 2.5f / d1) {
        t -= 2.25f / d1;
        return n1 * t * t + 0.9375f;
    } else {
        t -= 2.625f / d1;
        return n1 * t * t + 0.984375f;
    }
}

float SimplePuzzleRenderer::easeInOutElastic(float t) const {
    const float c5 = (2.0f * M_PI) / 4.5f;

    if (t == 0.0f) return 0.0f;
    if (t == 1.0f) return 1.0f;
    if (t < 0.5f) {
        t *= 2.0f;
        return -(std::pow(2.0f, 20.0f * t - 10.0f) * std::sin((20.0f * t - 11.25f) * c5)) / 2.0f;
    } else {
        t = (t - 0.5f) * 2.0f;
        return (std::pow(2.0f, -20.0f * t + 10.0f) * std::sin((20.0f * t - 11.25f) * c5)) / 2.0f + 1.0f;
    }
}

// Helper to get screen coordinates (top-left) for a grid cell
glm::vec2 SimplePuzzleRenderer::getScreenPosForGrid(int r, int c) const {
    float puzzleAreaWidth = PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing; // Changed spacing to tileSpacing
    float puzzleAreaHeight = PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing; // Changed spacing to tileSpacing
    float startX = (this->width - puzzleAreaWidth) / 2.0f; // Changed windowWidth to width
    float startY = (this->height - puzzleAreaHeight) / 2.0f; // Changed windowHeight to height
    return glm::vec2(startX + c * (tileSize + tileSpacing), startY + r * (tileSize + tileSpacing)); // Changed spacing to tileSpacing
}

void SimplePuzzleRenderer::startTileMoveAnimation(int tileValue, int fromGridR, int fromGridC, int toGridR, int toGridC) {
    if (tileValue == 0) return; // Cannot animate empty space directly
    movingTileValue = tileValue;
    movingTileStartScreenPos = getScreenPosForGrid(fromGridR, fromGridC);
    movingTileTargetScreenPos = getScreenPosForGrid(toGridR, toGridC);
    movingTileCurrentScreenPos = movingTileStartScreenPos; // Start at the beginning
    animationStartTime = static_cast<float>(glfwGetTime()); // Use glfwGetTime() for consistency
}

// リセット時のアニメーション開始
void SimplePuzzleRenderer::startResetAnimation(const Puzzle& puzzle) {
    // アニメーション中のタイルリストをクリア
    animatingTiles.clear();

    // 各タイルの現在位置と目標位置を計算
    for (int r = 0; r < PUZZLE_SIZE; r++) {
        for (int c = 0; c < PUZZLE_SIZE; c++) {
            int value = puzzle.getTile(r, c);
            if (value == 0) continue; // 空きマスはスキップ

            // 正しい位置を計算
            int targetRow = (value - 1) / PUZZLE_SIZE;
            int targetCol = (value - 1) % PUZZLE_SIZE;

            // 現在位置と正しい位置が異なる場合、アニメーションリストに追加
            if (r != targetRow || c != targetCol) {
                glm::vec2 startPos = getScreenPosForGrid(r, c);
                glm::vec2 targetPos = getScreenPosForGrid(targetRow, targetCol);

                AnimatingTile tile;
                tile.value = value;
                tile.startScreenPos = startPos;
                tile.currentScreenPos = startPos; // 開始位置から始める
                tile.targetScreenPos = targetPos;

                animatingTiles.push_back(tile);
            }
        }
    }

    // アニメーションを開始
    if (!animatingTiles.empty()) {
        isResetAnimation = true;
        animationStartTime = static_cast<float>(glfwGetTime());

        // 単一タイルアニメーション用の変数もクリア（後方互換性のため）
        movingTileValue = 0;
    }
}

bool SimplePuzzleRenderer::isAnimationRunning() const {
    // リセットアニメーション中かチェック
    if (isResetAnimation) {
        float elapsedTime = static_cast<float>(glfwGetTime()) - animationStartTime;
        return elapsedTime < RESET_ANIMATION_DURATION;
    }

    // 複数タイルのアニメーションがあるかチェック
    if (!animatingTiles.empty()) {
        float elapsedTime = static_cast<float>(glfwGetTime()) - animationStartTime;
        return elapsedTime < TILE_MOVE_DURATION;
    }

    // 単一タイルのアニメーション（後方互換性）
    if (movingTileValue == 0) return false;
    float elapsedTime = static_cast<float>(glfwGetTime()) - animationStartTime;
    return elapsedTime < TILE_MOVE_DURATION;
}

// This is the new render function with animation logic
void SimplePuzzleRenderer::render(Puzzle& puzzle, double mouseX, double mouseY, float currentTime) { // Removed const from Puzzle&
    // 2D描画の設定 (重複する可能性があるので、main.cpp側で一括して行うか確認)
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    glOrtho(0, this->width, this->height, 0, -1, 1); // Use this->width and this->height
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();

    // 描画状態の設定 (重複する可能性)
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);


    float puzzleAreaWidth = PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing; // Changed spacing to tileSpacing
    float puzzleAreaHeight = PUZZLE_SIZE * tileSize + (PUZZLE_SIZE - 1) * tileSpacing; // Changed spacing to tileSpacing
    float currentStartX = (this->width - puzzleAreaWidth) / 2.0f; // Changed windowWidth to width, and startX to currentStartX to avoid conflict with member
    float currentStartY = (this->height - puzzleAreaHeight) / 2.0f; // Changed windowHeight to height, and startY to currentStartY to avoid conflict with member

    // リセットアニメーションの更新
    if (isResetAnimation && !animatingTiles.empty()) {
        float elapsedTime = currentTime - animationStartTime;
        if (elapsedTime < RESET_ANIMATION_DURATION) {
            float t = elapsedTime / RESET_ANIMATION_DURATION;
            // より滑らかなイージング関数を使用
            float easedT = easeInOutCubic(t);

            // 全てのアニメーション中のタイルを更新
            for (auto& tile : animatingTiles) {
                tile.currentScreenPos = glm::mix(tile.startScreenPos, tile.targetScreenPos, easedT);
            }
        } else {
            // アニメーション終了
            for (auto& tile : animatingTiles) {
                tile.currentScreenPos = tile.targetScreenPos;
            }
            animatingTiles.clear();
            isResetAnimation = false;
        }
    }
    // 複数タイルのアニメーション更新（リセット以外）
    else if (!animatingTiles.empty()) {
        float elapsedTime = currentTime - animationStartTime;
        if (elapsedTime < TILE_MOVE_DURATION) {
            float t = elapsedTime / TILE_MOVE_DURATION;
            // より素早く移動するイージング関数を使用
            float easedT = easeInOutQuad(t);

            // 全てのアニメーション中のタイルを更新
            for (auto& tile : animatingTiles) {
                tile.currentScreenPos = glm::mix(tile.startScreenPos, tile.targetScreenPos, easedT);
            }
        } else {
            // アニメーション終了
            for (auto& tile : animatingTiles) {
                tile.currentScreenPos = tile.targetScreenPos;
            }
            animatingTiles.clear();
        }
    }
    // 単一タイルのアニメーション更新（後方互換性）
    else if (movingTileValue != 0) {
        float elapsedTime = currentTime - animationStartTime;
        if (elapsedTime < TILE_MOVE_DURATION) {
            float t = elapsedTime / TILE_MOVE_DURATION;
            // より素早く移動するイージング関数を使用
            float easedT = easeInOutQuad(t);
            movingTileCurrentScreenPos = glm::mix(movingTileStartScreenPos, movingTileTargetScreenPos, easedT);

            // デバッグ出力（アニメーションが動作していることを確認）
            // std::cout << "Animation running: t=" << t << ", easedT=" << easedT << std::endl;
        } else {
            // Animation finished
            movingTileCurrentScreenPos = movingTileTargetScreenPos;
            movingTileValue = 0; // Reset animation state
        }
    }

    for (int r = 0; r < PUZZLE_SIZE; ++r) {
        for (int c = 0; c < PUZZLE_SIZE; ++c) {
            int tileValue = puzzle.getTile(r, c);
            bool isCorrect = puzzle.isTileCorrect(r, c);
            float currentTileX = currentStartX + c * (tileSize + tileSpacing);
            float currentTileY = currentStartY + r * (tileSize + tileSpacing);
            glm::vec3 bgColor, textColor;

            // Determine base colors based on correctness (for fade effect)
            if (isCorrect) {
                bgColor = glm::vec3(238.0f / 255.0f, 238.0f / 255.0f, 238.0f / 255.0f);
                textColor = glm::vec3(17.0f / 255.0f, 17.0f / 255.0f, 17.0f / 255.0f);
            } else {
                bgColor = glm::vec3(17.0f / 255.0f, 17.0f / 255.0f, 17.0f / 255.0f);
                textColor = glm::vec3(238.0f / 255.0f, 238.0f / 255.0f, 238.0f / 255.0f);
            }

            // Fade in/out logic for correctness (simplified example)
            // This needs a way to track when a tile *becomes* correct/incorrect to trigger a timed fade.
            // For now, we'll just set alpha based on correctness directly, which is not a fade.
            // A proper fade would require storing the start time of the fade and its duration.
            // Let's assume for now we just want to show the state, and the movement is the primary animation.
            float alpha = 1.0f; // Full alpha for now

            if (tileValue == 0) { // Empty space
                // 空のピースは表示しない
                // スキップ
            } else if (tileValue == movingTileValue) {
                // This is the tile currently being animated, draw it at its animated position
                // Use the new drawTile and drawNumber signatures
                drawTile(movingTileCurrentScreenPos.x, movingTileCurrentScreenPos.y, tileSize, bgColor * alpha, true);
                // The drawNumber in the previous attempt had (int number, float x, float y, float tileSize, const glm::vec3& textColor, bool isCorrectlyPlaced)
                // The existing drawNumber is (float x, float y, int number, bool isCorrectlyPlaced)
                // Need to reconcile or use the existing one if it's preferred.
                // Assuming the new one from the previous attempt was intended for the new render:
                // drawNumber(tileValue, movingTileCurrentScreenPos.x, movingTileCurrentScreenPos.y, tileSize, textColor * alpha, isCorrect);
                // Using the new drawNumber signature
                drawNumber(tileValue, movingTileCurrentScreenPos.x + tileSize / 2.0f, movingTileCurrentScreenPos.y + tileSize / 2.0f, tileSize, textColor * alpha, isCorrect);

            } else {
                // Static tile
                // ... (rest of the logic for static tiles, checking if it's a target of animation etc.)
                // This part was complex and might need further refinement based on when puzzle state updates.
                // For now, drawing all non-moving tiles directly.
                bool shouldDraw = true;
                if (movingTileValue != 0) {
                    // Logic to avoid drawing the tile that is currently moving from its original spot,
                    // or the spot that the moving tile is going to occupy if the puzzle state has already updated.
                    // This depends heavily on how Puzzle::moveTile and startTileMoveAnimation interact.
                    // If Puzzle::moveTile updates the board *before* animation starts, then
                    // puzzle.getTile(r,c) will give the new state.
                    // The empty space is where the moving tile is going.
                    // The original position of the moving tile is now empty.

                    // Let's find the original grid position of the moving tile.
                    // This is tricky because the puzzle state might have already changed.
                    // A robust way would be for startTileMoveAnimation to store the original (r,c) of the moving tile.
                    // For now, we assume the `movingTileValue` is unique and not 0.
                    // If the current (r,c) is the empty spot (where the tile is moving TO),
                    // and this (r,c) *used* to contain the `movingTileValue` (which is now at `movingTileCurrentScreenPos`),
                    // then this spot should appear empty until the animation finishes.
                    // However, the `puzzle.getTile(r,c)` for the empty spot will return 0.

                    // We need to avoid drawing the tile that *was* at the start of the animation if it's not the one currently moving.
                    // This is complex. The simplest approach for now:
                    // The tile with `movingTileValue` is drawn at `movingTileCurrentScreenPos`.
                    // All other tiles are drawn at their grid positions based on `puzzle.getTile(r,c)`.
                    // The empty space (where `puzzle.getTile(r,c) == 0`) is drawn as an empty tile.
                    // This should correctly represent the state *during* animation if the puzzle board
                    // is updated *before* the animation call.
                }


                if (shouldDraw) {
                    // Use the new drawTile and drawNumber signatures
                    drawTile(currentTileX, currentTileY, tileSize, bgColor * alpha, true);
                    // drawNumber(tileValue, currentTileX, currentTileY, tileSize, textColor * alpha, isCorrect);
                    // Using the new drawNumber signature
                    drawNumber(tileValue, currentTileX + tileSize / 2.0f, currentTileY + tileSize / 2.0f, tileSize, textColor * alpha, isCorrect);
                }
            }
        }
    }

    // リセットアニメーション中のタイルを描画
    if (isResetAnimation) {
        for (const auto& tile : animatingTiles) {
            bool isCorrect = true; // リセットアニメーション中は全てのタイルが正しい位置に向かっている

            // Calculate alpha based on animation progress
            float elapsedTime = currentTime - animationStartTime;
            float alpha = 1.0f;
            if (elapsedTime < RESET_ANIMATION_DURATION) {
                alpha = elapsedTime / RESET_ANIMATION_DURATION;
            }

            // Draw the tile with the current background color
            glm::vec3 bgColor = solvedColor; // リセットアニメーション中は全て正解色
            drawTile(tile.currentScreenPos.x, tile.currentScreenPos.y, tileSize, bgColor * alpha, true);

            // Draw the number
            glm::vec3 textColor(1.0f, 1.0f, 1.0f); // White text
            drawNumber(tile.value, tile.currentScreenPos.x + tileSize / 2.0f, tile.currentScreenPos.y + tileSize / 2.0f, tileSize, textColor * alpha, isCorrect);
        }
    }
    // 複数タイルのアニメーション（リセット以外）
    else if (!animatingTiles.empty()) {
        for (const auto& tile : animatingTiles) {
            bool isCorrect = tile.value == (tile.targetScreenPos.y - currentStartY) / (tileSize + tileSpacing) * PUZZLE_SIZE +
                            (tile.targetScreenPos.x - currentStartX) / (tileSize + tileSpacing) + 1;

            // Calculate alpha based on animation progress
            float elapsedTime = currentTime - animationStartTime;
            float alpha = 1.0f;
            if (elapsedTime < TILE_MOVE_DURATION) {
                alpha = elapsedTime / TILE_MOVE_DURATION;
            }

            // Draw the tile with the current background color
            glm::vec3 bgColor = isCorrect ? solvedColor : normalColor;
            drawTile(tile.currentScreenPos.x, tile.currentScreenPos.y, tileSize, bgColor * alpha, true);

            // Draw the number
            glm::vec3 textColor(1.0f, 1.0f, 1.0f); // White text
            drawNumber(tile.value, tile.currentScreenPos.x + tileSize / 2.0f, tile.currentScreenPos.y + tileSize / 2.0f, tileSize, textColor * alpha, isCorrect);
        }
    }
    // 単一タイルのアニメーション（後方互換性）
    else if (movingTileValue != 0) {
        bool isCorrect = movingTileValue == (movingTileTargetScreenPos.y - currentStartY) / (tileSize + tileSpacing) * PUZZLE_SIZE +
                        (movingTileTargetScreenPos.x - currentStartX) / (tileSize + tileSpacing) + 1;

        // Calculate alpha based on animation progress
        float elapsedTime = currentTime - animationStartTime;
        float alpha = 1.0f;
        if (elapsedTime < TILE_MOVE_DURATION) {
            alpha = elapsedTime / TILE_MOVE_DURATION;
        }

        // Draw the tile with the current background color
        glm::vec3 bgColor = isCorrect ? solvedColor : normalColor;
        drawTile(movingTileCurrentScreenPos.x, movingTileCurrentScreenPos.y, tileSize, bgColor * alpha, true);

        // Draw the number
        glm::vec3 textColor(1.0f, 1.0f, 1.0f); // White text
        drawNumber(movingTileValue, movingTileCurrentScreenPos.x + tileSize / 2.0f, movingTileCurrentScreenPos.y + tileSize / 2.0f, tileSize, textColor * alpha, isCorrect);
    }

    // ... existing cleanup code like glUseProgram(0) ...
}

// For simplicity, I'll assume the shader handles a vec3 color and we are not doing fade yet.

void SimplePuzzleRenderer::drawTile(float x, float y, float size, const glm::vec3& bgColor, bool isRounded) const {
    // This is the new drawTile signature expected by the new render function.
    // The old drawTile was: void SimplePuzzleRenderer::drawTile(float x, float y, int value, bool isHovered, bool isCorrectlyPlaced)
    // We need to adapt the body of this function or ensure the correct one is called.
    // For now, let's assume this new signature is intended and we need to fill its body
    // based on the old one, but using 'size' and 'bgColor'.

    // Set color using bgColor
    glColor3fv(glm::value_ptr(bgColor));
    glDisable(GL_TEXTURE_2D); // Assuming no textures for tiles for now

    const float r = size * 0.1f; // Use 'size' parameter
    const int num_segments = 10;

    glBegin(GL_POLYGON);
    auto draw_vertex_with_tex = [&](float vx, float vy) {
        glVertex2f(vx, vy);
    };

    // Top-left corner
    for (int i = 0; i <= num_segments; ++i) {
        float angle = M_PI - (M_PI / 2.0f) * (static_cast<float>(i) / static_cast<float>(num_segments));
        draw_vertex_with_tex(x + r + cos(angle) * r, y + r - sin(angle) * r);
    }
    // Top-right corner
    for (int i = 0; i <= num_segments; ++i) {
        float angle = (M_PI / 2.0f) - (M_PI / 2.0f) * (static_cast<float>(i) / static_cast<float>(num_segments));
        draw_vertex_with_tex(x + size - r + cos(angle) * r, y + r - sin(angle) * r);
    }
    // Bottom-right corner
    for (int i = 0; i <= num_segments; ++i) {
        float angle = 0.0f - (M_PI / 2.0f) * (static_cast<float>(i) / static_cast<float>(num_segments));
        draw_vertex_with_tex(x + size - r + cos(angle) * r, y + size - r - sin(angle) * r);
    }
    // Bottom-left corner
    for (int i = 0; i <= num_segments; ++i) {
        float angle = (-M_PI / 2.0f) - (M_PI / 2.0f) * (static_cast<float>(i) / static_cast<float>(num_segments));
        draw_vertex_with_tex(x + r + cos(angle) * r, y + size - r - sin(angle) * r);
    }
    glEnd();
}

void SimplePuzzleRenderer::drawNumber(int number, float x, float y, float currentTileSize, const glm::vec3& textColor, bool isCorrectlyPlaced) const {
    // This is the new drawNumber signature expected by the new render function.
    // The old drawNumber was: void SimplePuzzleRenderer::drawNumber(float x, float y, int number, bool isCorrectlyPlaced)
    // This new signature adds tileSize and textColor. We need to use them.
    // The old one determined color internally.

    // Set color using textColor
    glColor3fv(glm::value_ptr(textColor));

    std::string numStr = std::to_string(number);
    float baseSize = currentTileSize * 0.35f;
    float segmentLength = baseSize / 2.0f;
    float lineWidth = baseSize / 10.0f;

    // Centering logic: x, y are passed as center of the number
    // The drawDigit function expects currentX, currentY as the center for the digit.

    glLineWidth(lineWidth); // Set line width once

    auto drawDigit = [&](float currentX_digit, float currentY_digit, int digit_val) { // Renamed params to avoid conflict
        // ... (7-segment drawing logic from the old drawNumber)
        // This logic uses currentX_digit and currentY_digit as the center for the digit.
        // ... existing 7-segment drawing logic ...
        glLineWidth(lineWidth);
        auto drawSegment = [&](bool top, bool topRight, bool bottomRight, bool bottom, bool bottomLeft, bool topLeft, bool middle) {
            if (top) {
                glBegin(GL_LINES);
                glVertex2f(currentX_digit - segmentLength/2, currentY_digit - segmentLength);
                glVertex2f(currentX_digit + segmentLength/2, currentY_digit - segmentLength);
                glEnd();
            }
            if (topRight) {
                glBegin(GL_LINES);
                glVertex2f(currentX_digit + segmentLength/2, currentY_digit - segmentLength);
                glVertex2f(currentX_digit + segmentLength/2, currentY_digit);
                glEnd();
            }
            if (bottomRight) {
                glBegin(GL_LINES);
                glVertex2f(currentX_digit + segmentLength/2, currentY_digit);
                glVertex2f(currentX_digit + segmentLength/2, currentY_digit + segmentLength);
                glEnd();
            }
            if (bottom) {
                glBegin(GL_LINES);
                glVertex2f(currentX_digit - segmentLength/2, currentY_digit + segmentLength);
                glVertex2f(currentX_digit + segmentLength/2, currentY_digit + segmentLength);
                glEnd();
            }
            if (bottomLeft) {
                glBegin(GL_LINES);
                glVertex2f(currentX_digit - segmentLength/2, currentY_digit);
                glVertex2f(currentX_digit - segmentLength/2, currentY_digit + segmentLength);
                glEnd();
            }
            if (topLeft) {
                glBegin(GL_LINES);
                glVertex2f(currentX_digit - segmentLength/2, currentY_digit - segmentLength);
                glVertex2f(currentX_digit - segmentLength/2, currentY_digit);
                glEnd();
            }
            if (middle) {
                glBegin(GL_LINES);
                glVertex2f(currentX_digit - segmentLength/2, currentY_digit);
                glVertex2f(currentX_digit + segmentLength/2, currentY_digit);
                glEnd();
            }
        };

        switch (digit_val) {
            case 0: drawSegment(true, true, true, true, true, true, false); break;
            case 1: drawSegment(false, true, true, false, false, false, false); break;
            case 2: drawSegment(true, true, false, true, true, false, true); break;
            case 3: drawSegment(true, true, true, true, false, false, true); break;
            case 4: drawSegment(false, true, true, false, false, true, true); break;
            case 5: drawSegment(true, false, true, true, false, true, true); break;
            case 6: drawSegment(true, false, true, true, true, true, true); break;
            case 7: drawSegment(true, true, true, false, false, false, false); break;
            case 8: drawSegment(true, true, true, true, true, true, true); break;
            case 9: drawSegment(true, true, true, true, false, true, true); break;
        }
    };

    if (number < 10) {
        drawDigit(x, y, number); // x, y are already the center
    } else {
        int tensDigit = number / 10;
        int onesDigit = number % 10;
        float digitSpacing = segmentLength * 0.75f;
        drawDigit(x - digitSpacing, y, tensDigit);
        drawDigit(x + digitSpacing, y, onesDigit);
    }
    // Removed glPopMatrix as it's not matched with a glPushMatrix in this function.
    // It was present in the old drawNumber, but its corresponding push was likely outside or in the calling render loop.
    // For fixed-function OpenGL, matrix stack management is important.
    // If numbers need their own transformations, a push/pop pair would be needed here.
    // Given x,y is the center, direct drawing is fine.
}
