[{"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/imgui/imgui.cpp.o", "src/imgui/imgui.cpp"], "file": "src/imgui/imgui.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/imgui/imgui_draw.cpp.o", "src/imgui/imgui_draw.cpp"], "file": "src/imgui/imgui_draw.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/imgui/imgui_impl_glfw.cpp.o", "src/imgui/imgui_impl_glfw.cpp"], "file": "src/imgui/imgui_impl_glfw.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/imgui/imgui_impl_opengl3.cpp.o", "src/imgui/imgui_impl_opengl3.cpp"], "file": "src/imgui/imgui_impl_opengl3.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/imgui/imgui_tables.cpp.o", "src/imgui/imgui_tables.cpp"], "file": "src/imgui/imgui_tables.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/imgui/imgui_widgets.cpp.o", "src/imgui/imgui_widgets.cpp"], "file": "src/imgui/imgui_widgets.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/audio_system.cpp.o", "src/audio_system.cpp"], "file": "src/audio_system.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/main.cpp.o", "src/main.cpp"], "file": "src/main.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/puzzle.cpp.o", "src/puzzle.cpp"], "file": "src/puzzle.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/puzzle_renderer.cpp.o", "src/puzzle_renderer.cpp"], "file": "src/puzzle_renderer.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/simple_puzzle_renderer.cpp.o", "src/simple_puzzle_renderer.cpp"], "file": "src/simple_puzzle_renderer.cpp"}, {"directory": "/mnt/ExtremeSSD/Dev-Projects/C-and-C++/15Puzzle-C", "arguments": ["/nix/store/7nlf5v84s4p2yhx327j8495yik60qnzh-gcc-wrapper-14.3.0/bin/g++", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-Iinclude/imgui", "-Ibuild/.gens/15Puzzle-C/linux/x86_64/release/rules/utils/bin2c", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-isystem", "/home/<USER>/.xmake/packages/f/fontconfig/2.14.2/0ef664274caa4032bba74971a4e50d0d/include", "-isystem", "/home/<USER>/.xmake/packages/f/freetype/2.13.1/22773b67079e4fc8a5c5e1f2b2afdc21/include/freetype2", "-isystem", "/home/<USER>/.xmake/packages/z/zlib/v1.3.1/994fafa590ed48ac9f71516cc846d155/include", "-isystem", "/home/<USER>/.xmake/packages/e/expat/2.7.1/73d8bdcb978640de9841c948f87f7e1d/include", "-isystem", "/nix/store/p5sr7yhdya9gfa17pnh0fv6csqkrwfqq-libXrandr-1.5.4-dev/include", "-isystem", "/nix/store/n0v2qx7l8pddghxh0wdgl338hcqq4axr-libXinerama-1.1.5-dev/include", "-isystem", "/nix/store/k3f4091l02lqqr0kp5myrccs9yp60p8h-libXcursor-1.2.3-dev/include", "-isystem", "/nix/store/qm8w11nch1vj2zrkfijmdsmcnxw0ma92-libXrender-0.9.12-dev/include", "-isystem", "/nix/store/dq8drsfycnld3h65d3csazznfrmq6mjk-libXi-1.8.2-dev/include", "-isystem", "/nix/store/47hg17byzghi0cy8wcldskpa0fg6a43s-libXfixes-6.0.1-dev/include", "-isystem", "/nix/store/4mv872ij8d5m4zwyc93b552nsiavh23s-libXext-1.3.6-dev/include", "-isystem", "/nix/store/8xbbdx4ckcdj76ldb0cbym965whipq72-libglvnd-1.7.0-dev/include", "-isystem", "/nix/store/wqsvj8psx9lyjcw4rhydylbvdjf1pc30-glew-2.2.0-dev/include", "-isystem", "/nix/store/wakcjb523m43qbm4xblyqm7rgg9l9s32-glu-9.0.3-dev/include", "-isystem", "/nix/store/b5i8r0l5bnaj8khjz4lsmsdph8dkha3s-libX11-1.8.12-dev/include", "-isystem", "/nix/store/l1qkka1svxk8s0myynmb6kgl0ni19mjk-xorgproto-2024.1/include", "-isystem", "/nix/store/y91x77rjip3i5zdza2ikf2lj80qc0286-libxcb-1.17.0-dev/include", "-isystem", "/nix/store/kccsaaa86dads64yfyd6f6lhp6p5righ-libXau-1.0.12-dev/include", "-isystem", "/home/<USER>/.xmake/packages/g/glm/1.0.1/1781f3ac6d8141628505a9ae557cd017/include", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build/.objs/15Puzzle-C/linux/x86_64/release/src/text_renderer.cpp.o", "src/text_renderer.cpp"], "file": "src/text_renderer.cpp"}]