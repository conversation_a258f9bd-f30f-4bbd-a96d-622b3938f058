[{"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\imgui\\imgui.cpp.obj", "src\\imgui\\imgui.cpp"], "file": "src\\imgui\\imgui.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\imgui\\imgui_draw.cpp.obj", "src\\imgui\\imgui_draw.cpp"], "file": "src\\imgui\\imgui_draw.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\imgui\\imgui_impl_glfw.cpp.obj", "src\\imgui\\imgui_impl_glfw.cpp"], "file": "src\\imgui\\imgui_impl_glfw.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\imgui\\imgui_impl_opengl3.cpp.obj", "src\\imgui\\imgui_impl_opengl3.cpp"], "file": "src\\imgui\\imgui_impl_opengl3.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\imgui\\imgui_tables.cpp.obj", "src\\imgui\\imgui_tables.cpp"], "file": "src\\imgui\\imgui_tables.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\imgui\\imgui_widgets.cpp.obj", "src\\imgui\\imgui_widgets.cpp"], "file": "src\\imgui\\imgui_widgets.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\audio_system.cpp.obj", "src\\audio_system.cpp"], "file": "src\\audio_system.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\main.cpp.obj", "src\\main.cpp"], "file": "src\\main.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\puzzle.cpp.obj", "src\\puzzle.cpp"], "file": "src\\puzzle.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\puzzle_renderer.cpp.obj", "src\\puzzle_renderer.cpp"], "file": "src\\puzzle_renderer.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\simple_puzzle_renderer.cpp.obj", "src\\simple_puzzle_renderer.cpp"], "file": "src\\simple_puzzle_renderer.cpp"}, {"directory": "g:\\Dev-Projects\\C-and-C++\\15Puzzle-C", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-O3", "-std=c++17", "-Iin<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>\\core", "-Iinclude\\imgui", "-Ibuild\\.gens\\15Puzzle-C\\mingw\\x86_64\\release\\rules\\utils\\bin2c", "-IC:\\msys64\\mingw64\\bin\\..\\include\\freetype2", "-IC:\\msys64\\mingw64\\bin\\..\\include\\libpng16", "-IC:\\msys64\\mingw64\\bin\\..\\include\\harfbuzz", "-IC:\\msys64\\mingw64\\bin\\..\\include\\glib-2.0", "-IC:\\msys64\\mingw64\\bin\\..\\lib\\glib-2.0\\include", "-IC:\\msys64\\mingw64\\bin\\..\\include", "-DPLATFORM_DESKTOP", "-DIMGUI_IMPL_OPENGL_LOADER_GLEW", "-O3", "-mtune=native", "-march=native", "-mfpmath=both", "-DNDEBUG", "-o", "build\\.objs\\15Puzzle-C\\mingw\\x86_64\\release\\src\\text_renderer.cpp.obj", "src\\text_renderer.cpp"], "file": "src\\text_renderer.cpp"}]